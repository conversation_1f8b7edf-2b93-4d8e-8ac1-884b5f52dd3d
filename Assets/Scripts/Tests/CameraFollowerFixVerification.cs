using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 用于验证CameraFollower修复的简单脚本
    /// 可以在Unity编辑器中手动运行来验证修复效果
    /// </summary>
    public class CameraFollowerFixVerification : MonoBehaviour
    {
        [Header("测试组件")]
        public CameraFollower cameraFollower;
        public Transform targetObject;
        public UnityEngine.Camera cameraObject;
        
        [Header("测试结果")]
        public bool testPassed = false;
        public string testResult = "";
        
        [ContextMenu("运行边界禁用测试")]
        public void RunBoundsDisableTest()
        {
            if (cameraFollower == null || targetObject == null || cameraObject == null)
            {
                testResult = "测试组件未设置完整";
                testPassed = false;
                Debug.LogError(testResult);
                return;
            }

            try
            {
                Debug.Log("=== 开始边界禁用测试 ===");

                // 设置测试环境
                Rect bounds = new Rect(-2, -2, 4, 4);
                Debug.Log($"1. 设置边界: {bounds}");
                cameraFollower.SetCameraBounds(bounds);

                Debug.Log($"2. 设置跟随目标，初始位置: {targetObject.transform.position}");
                cameraFollower.SetFollowTarget(targetObject.transform);

                Debug.Log($"3. 摄像机初始位置: {cameraObject.transform.position}");

                // 禁用边界
                Debug.Log("4. 禁用边界");
                cameraFollower.EnableBounds(false);

                Debug.Log($"5. 禁用边界后摄像机位置: {cameraObject.transform.position}");

                // 移动目标到边界外
                Debug.Log("6. 移动目标到 (20, 0, 0)");
                targetObject.transform.position = new Vector3(20, 0, 0);

                // 更新摄像机位置
                Debug.Log("7. 调用 UpdateCameraPosition()");
                cameraFollower.UpdateCameraPosition();

                // 检查结果
                Vector3 cameraPos = cameraObject.transform.position;
                bool passed = cameraPos.x > bounds.xMax; // 应该 > 2.0f

                testPassed = passed;
                testResult = $"摄像机位置: {cameraPos}, X值: {cameraPos.x}, 边界最大X: {bounds.xMax}, 测试{(passed ? "通过" : "失败")}";

                Debug.Log("=== 测试结果 ===");
                if (passed)
                {
                    Debug.Log($"✅ 测试通过: {testResult}");
                }
                else
                {
                    Debug.LogError($"❌ 测试失败: {testResult}");
                }
            }
            catch (System.Exception e)
            {
                testResult = $"测试异常: {e.Message}";
                testPassed = false;
                Debug.LogError(testResult);
            }
        }
        
        [ContextMenu("设置测试环境")]
        public void SetupTestEnvironment()
        {
            // 自动查找或创建测试组件
            if (cameraFollower == null)
            {
                cameraFollower = FindObjectOfType<CameraFollower>();
                if (cameraFollower == null)
                {
                    GameObject cameraGO = new GameObject("TestCamera");
                    cameraObject = cameraGO.AddComponent<UnityEngine.Camera>();
                    cameraFollower = cameraGO.AddComponent<CameraFollower>();
                }
            }
            
            if (cameraObject == null && cameraFollower != null)
            {
                cameraObject = cameraFollower.GetComponent<UnityEngine.Camera>();
            }
            
            if (targetObject == null)
            {
                GameObject targetGO = new GameObject("TestTarget");
                targetObject = targetGO.transform;
                targetObject.position = Vector3.zero;
            }
            
            Debug.Log("测试环境设置完成");
        }
    }
}
