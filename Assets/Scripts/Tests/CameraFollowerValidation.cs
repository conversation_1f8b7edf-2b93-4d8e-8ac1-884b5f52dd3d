using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraFollower功能验证脚本
    /// 用于手动验证修复后的功能是否正常工作
    /// </summary>
    public class CameraFollowerValidation : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private bool runValidation = false;
        [SerializeField] private Transform testTarget;
        
        private CameraFollower cameraFollower;
        private UnityEngine.Camera testCamera;
        
        private void Start()
        {
            if (runValidation)
            {
                StartValidation();
            }
        }
        
        private void StartValidation()
        {
            Debug.Log("=== CameraFollower 功能验证开始 ===");
            
            // 创建测试摄像机
            GameObject cameraObj = new GameObject("TestCamera");
            testCamera = cameraObj.AddComponent<UnityEngine.Camera>();
            testCamera.orthographic = true;
            testCamera.orthographicSize = 5f;
            
            // 添加CameraFollower组件
            cameraFollower = cameraObj.AddComponent<CameraFollower>();
            
            // 创建测试目标
            if (testTarget == null)
            {
                GameObject targetObj = new GameObject("TestTarget");
                testTarget = targetObj.transform;
                testTarget.position = Vector3.zero;
            }
            
            // 执行各项功能测试
            TestSetFollowTarget();
            TestSetOffset();
            TestBoundsLimiting();
            TestEnableBounds();
            
            Debug.Log("=== CameraFollower 功能验证完成 ===");
        }
        
        private void TestSetFollowTarget()
        {
            Debug.Log("测试1: SetFollowTarget功能");
            
            Vector3 initialCameraPos = cameraFollower.transform.position;
            cameraFollower.SetFollowTarget(testTarget);
            
            // 等待一帧让位置更新
            StartCoroutine(DelayedCheck(() =>
            {
                Vector3 expectedPos = testTarget.position + new Vector3(0, 1, -10);
                Vector3 actualPos = cameraFollower.transform.position;
                
                float distance = Vector3.Distance(expectedPos, actualPos);
                if (distance < 0.1f)
                {
                    Debug.Log("✓ SetFollowTarget测试通过");
                }
                else
                {
                    Debug.LogError($"✗ SetFollowTarget测试失败 - 期望位置: {expectedPos}, 实际位置: {actualPos}");
                }
            }));
        }
        
        private void TestSetOffset()
        {
            Debug.Log("测试2: SetOffset功能");
            
            Vector3 newOffset = new Vector3(2, 0, -10);
            cameraFollower.SetOffset(newOffset);
            
            StartCoroutine(DelayedCheck(() =>
            {
                Vector3 expectedPos = testTarget.position + newOffset;
                Vector3 actualPos = cameraFollower.transform.position;
                
                float distance = Vector3.Distance(expectedPos, actualPos);
                if (distance < 0.1f)
                {
                    Debug.Log("✓ SetOffset测试通过");
                }
                else
                {
                    Debug.LogError($"✗ SetOffset测试失败 - 期望位置: {expectedPos}, 实际位置: {actualPos}");
                }
            }));
        }
        
        private void TestBoundsLimiting()
        {
            Debug.Log("测试3: 边界限制功能");
            
            // 设置一个小边界
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraFollower.SetCameraBounds(bounds);
            
            // 将目标移动到边界外
            testTarget.position = new Vector3(10, 0, 0);
            cameraFollower.SetFollowTarget(testTarget);
            
            StartCoroutine(DelayedCheck(() =>
            {
                Vector3 cameraPos = cameraFollower.transform.position;
                
                // 计算摄像机应该被限制的最大X位置
                float cameraWidth = testCamera.orthographicSize * 2f * testCamera.aspect;
                float maxAllowedX = bounds.xMax - cameraWidth * 0.5f;
                
                if (cameraPos.x <= maxAllowedX + 0.1f)
                {
                    Debug.Log($"✓ 边界限制测试通过 - 摄像机X位置: {cameraPos.x}, 最大允许: {maxAllowedX}");
                }
                else
                {
                    Debug.LogError($"✗ 边界限制测试失败 - 摄像机X位置: {cameraPos.x}, 最大允许: {maxAllowedX}");
                }
            }));
        }
        
        private void TestEnableBounds()
        {
            Debug.Log("测试4: EnableBounds功能");
            
            // 先启用边界
            cameraFollower.EnableBounds(true);
            Vector3 boundedPos = cameraFollower.transform.position;
            
            // 然后禁用边界
            cameraFollower.EnableBounds(false);
            
            StartCoroutine(DelayedCheck(() =>
            {
                Vector3 unboundedPos = cameraFollower.transform.position;
                Vector3 expectedPos = testTarget.position + new Vector3(2, 0, -10);
                
                float distance = Vector3.Distance(expectedPos, unboundedPos);
                if (distance < 0.1f)
                {
                    Debug.Log("✓ EnableBounds测试通过 - 禁用边界后摄像机可以移动到目标位置");
                }
                else
                {
                    Debug.LogError($"✗ EnableBounds测试失败 - 期望位置: {expectedPos}, 实际位置: {unboundedPos}");
                }
            }));
        }
        
        private System.Collections.IEnumerator DelayedCheck(System.Action action)
        {
            yield return new WaitForEndOfFrame();
            action?.Invoke();
        }
    }
}