%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &319271720 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -8653719598885068355, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
    type: 3}
  m_PrefabInstance: {fileID: 1534322105}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &319271721
Animator:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 319271720}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 50f7783907c884ea4b77c26c3b5227d8, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!1 &1353557381
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1353557384}
  - component: {fileID: 1353557383}
  - component: {fileID: 1353557382}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1353557382
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1353557381}
  m_Enabled: 1
--- !u!20 &1353557383
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1353557381}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.1, g: 0.1, b: 0.1, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 4
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1353557384
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1353557381}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1534322105
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8776491514796669712, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -0.69385105
      objectReference: {fileID: 0}
    - target: {fileID: -8659182146490653914, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 83.05136
      objectReference: {fileID: 0}
    - target: {fileID: -8653719598885068355, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_Name
      value: Plunkah
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -2.5
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7552582706839291426, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -3726474368768907445, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -108.981636
      objectReference: {fileID: 0}
    - target: {fileID: -2991845289374671003, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 5.8573856
      objectReference: {fileID: 0}
    - target: {fileID: -2570085443267176814, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 2.57872
      objectReference: {fileID: 0}
    - target: {fileID: -2306483184134476025, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 13.122915
      objectReference: {fileID: 0}
    - target: {fileID: -2125435715994292245, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -100.63013
      objectReference: {fileID: 0}
    - target: {fileID: -1662775209544061073, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 46.828358
      objectReference: {fileID: 0}
    - target: {fileID: -1072677548124905581, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -88.37833
      objectReference: {fileID: 0}
    - target: {fileID: 527219229850989764, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -10.570658
      objectReference: {fileID: 0}
    - target: {fileID: 703181390434973940, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 1.0037102
      objectReference: {fileID: 0}
    - target: {fileID: 2565510360539687927, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -80.66008
      objectReference: {fileID: 0}
    - target: {fileID: 5967889810190792300, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -35.523052
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: -8653719598885068355, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 319271721}
  m_SourcePrefab: {fileID: 4843985084834002234, guid: 5faacb9c2cc414a89b892a5b0bf295bd,
    type: 3}
