# What's new in version 10.0

## Added
- [Shadow Caster 2D](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@15.0/manual/2DShadows.html) support for [Sprite Skins](SpriteSkin.md) in URP projects.
- Optional [Sprite Skin](SpriteSkin.md) GPU deformation in URP projects.
- Frustum culling for [IK Solvers](2DIK.md#ik-solvers).

## Updated
- Simplified [Sprite Library Component](SL-component.md) inspector.