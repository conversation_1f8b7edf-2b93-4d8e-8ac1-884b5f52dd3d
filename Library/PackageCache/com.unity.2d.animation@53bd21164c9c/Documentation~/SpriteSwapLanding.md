# Sprite Swap
Use **Sprite Swap** to change a GameObject's rendered Sprite at runtime. You can swap the entire set of Sprites that make up a character (referred to as an 'actor') at once, or swap specific Sprites and 'parts' of an actor to create animation loops or other game-related features.

For various examples of how you can use this feature in a Project, [import sample Projects](Examples.md) for the 2D Animation package and refer to the [Sprite Swap examples](ex-sprite-swap.md) for examples of the different ways you can use Sprite Swap in your Projects.

**Topic**  |  **Description**
:----------|:----------------
[Introduction to Sprite Swap](SpriteSwapIntro.md)  |  Understand Sprite Swap, its requirements and limitations.
[Sprite Library Asset in Unity](SL-Asset.md) | Understand what the Sprite Library Asset is and how to use its features.
[Sprite Library Editor fundamentals](SL-Editor.md) | Understand how to use the Sprite Library Editor's main features.
[Overrides to the Main Library](SL-Main-Library.md) | Create overrides are and understand how to use them to make changes.
[Drag sprites to create or edit Categories and Labels](SL-Drag.md) | Drag sprites to perform certain functions in the Sprite Library Editor automatically.
[Sprite Library component in Unity](SL-component.md) | Understand what the Sprite Library component is and how to use its features.
[Sprite Resolver component in Unity](SL-Resolver.md) | Understand what the Sprite Resolver component is and how to use it.
[Setting up Sprite Swap](SpriteSwapSetup.md)  |  Understand how to set up the different components and assets needed to use Sprite Swap.

## Additional resources
* [PSD Importer package](https://docs.unity3d.com/Packages/com.unity.2d.psdimporter@latest)