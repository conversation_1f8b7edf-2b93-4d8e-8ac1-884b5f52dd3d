{"name": "Unity.2D.Animation.Runtime", "rootNamespace": "", "references": ["Unity.2D.Common.Runtime", "Unity.InternalAPIEngineBridge.001", "Unity.Mathematics", "Unity.Burst", "Unity.Collections", "Unity.RenderPipelines.Universal.Runtime"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.render-pipelines.universal", "expression": "1.0.0", "define": "ENABLE_URP"}, {"name": "com.unity.collections", "expression": "2.0.0-exp", "define": "COLLECTIONS_2_0_OR_ABOVE"}], "noEngineReferences": false}