---
uid: neon-intrinsics
---

# Burst Arm Neon intrinsics reference

This page contains an ordered reference for the APIs in [Unity.Burst.Intrinsics.Arm.Neon](xref:Unity.Burst.Intrinsics.Arm.Neon). For information on how to use these, refer to [Processor specific SIMD extensions](csharp-burst-intrinsics-processors.md).

### Intrinsics type creation and conversion

|Operation|Description|APIs|
|---|---|---|
|vcreate|Create vector|<details><summary>Click here to expand the API list</summary>[vcreate_f16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_f16*)<br/>[vcreate_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_f32*)<br/>[vcreate_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_f64*)<br/>[vcreate_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_s16*)<br/>[vcreate_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_s32*)<br/>[vcreate_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_s64*)<br/>[vcreate_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_s8*)<br/>[vcreate_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_u16*)<br/>[vcreate_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_u32*)<br/>[vcreate_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_u64*)<br/>[vcreate_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcreate_u8*)<br/></details>|
|vdup_n|Duplicate (splat) value|<details><summary>Click here to expand the API list</summary>[vdup_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_f32*)<br/>[vdup_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_f64*)<br/>[vdup_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_s16*)<br/>[vdup_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_s32*)<br/>[vdup_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_s64*)<br/>[vdup_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_s8*)<br/>[vdup_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_u16*)<br/>[vdup_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_u32*)<br/>[vdup_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_u64*)<br/>[vdup_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_n_u8*)<br/>[vdupq_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_f32*)<br/>[vdupq_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_f64*)<br/>[vdupq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_s16*)<br/>[vdupq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_s32*)<br/>[vdupq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_s64*)<br/>[vdupq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_s8*)<br/>[vdupq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_u16*)<br/>[vdupq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_u32*)<br/>[vdupq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_u64*)<br/>[vdupq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_n_u8*)<br/></details>|
|vdup_lane|Duplicate (splat) vector element|<details><summary>Click here to expand the API list</summary>[vdup_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_f32*)<br/>[vdup_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_f64*)<br/>[vdup_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_s16*)<br/>[vdup_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_s32*)<br/>[vdup_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_s64*)<br/>[vdup_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_s8*)<br/>[vdup_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_u16*)<br/>[vdup_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_u32*)<br/>[vdup_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_u64*)<br/>[vdup_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_lane_u8*)<br/>[vdup_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_f32*)<br/>[vdup_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_f64*)<br/>[vdup_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_s16*)<br/>[vdup_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_s32*)<br/>[vdup_laneq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_s64*)<br/>[vdup_laneq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_s8*)<br/>[vdup_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_u16*)<br/>[vdup_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_u32*)<br/>[vdup_laneq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_u64*)<br/>[vdup_laneq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdup_laneq_u8*)<br/>[vdupq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_f32*)<br/>[vdupq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_f64*)<br/>[vdupq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_s16*)<br/>[vdupq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_s32*)<br/>[vdupq_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_s64*)<br/>[vdupq_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_s8*)<br/>[vdupq_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_u16*)<br/>[vdupq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_u32*)<br/>[vdupq_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_u64*)<br/>[vdupq_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_lane_u8*)<br/>[vdupq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_f32*)<br/>[vdupq_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_f64*)<br/>[vdupq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_s16*)<br/>[vdupq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_s32*)<br/>[vdupq_laneq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_s64*)<br/>[vdupq_laneq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_s8*)<br/>[vdupq_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_u16*)<br/>[vdupq_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_u32*)<br/>[vdupq_laneq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_u64*)<br/>[vdupq_laneq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupq_laneq_u8*)<br/>[vdups_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdups_lane_f32*)<br/>[vdups_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdups_lane_s32*)<br/>[vdups_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdups_lane_u32*)<br/>[vdups_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdups_laneq_f32*)<br/>[vdups_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdups_laneq_s32*)<br/>[vdups_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdups_laneq_u32*)<br/>[vdupb_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupb_lane_s8*)<br/>[vdupb_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupb_lane_u8*)<br/>[vdupb_laneq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupb_laneq_s8*)<br/>[vdupb_laneq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupb_laneq_u8*)<br/>[vdupd_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupd_lane_f64*)<br/>[vdupd_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupd_lane_s64*)<br/>[vdupd_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupd_lane_u64*)<br/>[vdupd_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupd_laneq_f64*)<br/>[vdupd_laneq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupd_laneq_s64*)<br/>[vdupd_laneq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdupd_laneq_u64*)<br/>[vduph_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vduph_lane_s16*)<br/>[vduph_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vduph_lane_u16*)<br/>[vduph_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vduph_laneq_s16*)<br/>[vduph_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vduph_laneq_u16*)<br/></details>|
|vmov_n|Duplicate (splat) value|<details><summary>Click here to expand the API list</summary>[vmov_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_f32*)<br/>[vmov_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_f64*)<br/>[vmov_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_s16*)<br/>[vmov_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_s32*)<br/>[vmov_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_s64*)<br/>[vmov_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_s8*)<br/>[vmov_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_u16*)<br/>[vmov_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_u32*)<br/>[vmov_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_u64*)<br/>[vmov_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmov_n_u8*)<br/>[vmovq_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_f32*)<br/>[vmovq_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_f64*)<br/>[vmovq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_s16*)<br/>[vmovq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_s32*)<br/>[vmovq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_s64*)<br/>[vmovq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_s8*)<br/>[vmovq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_u16*)<br/>[vmovq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_u32*)<br/>[vmovq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_u64*)<br/>[vmovq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovq_n_u8*)<br/></details>|
|vcopy_lane|Insert vector element from another vector element|<details><summary>Click here to expand the API list</summary>[vcopy_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_f32*)<br/>[vcopy_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_f64*)<br/>[vcopy_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_s16*)<br/>[vcopy_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_s32*)<br/>[vcopy_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_s64*)<br/>[vcopy_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_s8*)<br/>[vcopy_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_u16*)<br/>[vcopy_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_u32*)<br/>[vcopy_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_u64*)<br/>[vcopy_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_lane_u8*)<br/>[vcopy_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_f32*)<br/>[vcopy_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_f64*)<br/>[vcopy_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_s16*)<br/>[vcopy_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_s32*)<br/>[vcopy_laneq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_s64*)<br/>[vcopy_laneq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_s8*)<br/>[vcopy_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_u16*)<br/>[vcopy_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_u32*)<br/>[vcopy_laneq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_u64*)<br/>[vcopy_laneq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopy_laneq_u8*)<br/>[vcopyq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_f32*)<br/>[vcopyq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_f64*)<br/>[vcopyq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_s16*)<br/>[vcopyq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_s32*)<br/>[vcopyq_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_s64*)<br/>[vcopyq_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_s8*)<br/>[vcopyq_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_u16*)<br/>[vcopyq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_u32*)<br/>[vcopyq_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_u64*)<br/>[vcopyq_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_lane_u8*)<br/>[vcopyq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_f32*)<br/>[vcopyq_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_f64*)<br/>[vcopyq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_s16*)<br/>[vcopyq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_s32*)<br/>[vcopyq_laneq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_s64*)<br/>[vcopyq_laneq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_s8*)<br/>[vcopyq_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_u16*)<br/>[vcopyq_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_u32*)<br/>[vcopyq_laneq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_u64*)<br/>[vcopyq_laneq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcopyq_laneq_u8*)<br/></details>|
|vcombine|Join two vectors into a larger vector|<details><summary>Click here to expand the API list</summary>[vcombine_f16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_f16*)<br/>[vcombine_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_f32*)<br/>[vcombine_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_f64*)<br/>[vcombine_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_s16*)<br/>[vcombine_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_s32*)<br/>[vcombine_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_s64*)<br/>[vcombine_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_s8*)<br/>[vcombine_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_u16*)<br/>[vcombine_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_u32*)<br/>[vcombine_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_u64*)<br/>[vcombine_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcombine_u8*)<br/></details>|
|vget_high|Get the higher half of the vector|<details><summary>Click here to expand the API list</summary>[vget_high_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_f32*)<br/>[vget_high_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_f64*)<br/>[vget_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_s16*)<br/>[vget_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_s32*)<br/>[vget_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_s64*)<br/>[vget_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_s8*)<br/>[vget_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_u16*)<br/>[vget_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_u32*)<br/>[vget_high_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_u64*)<br/>[vget_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_high_u8*)<br/></details>|
|vget_low|Get the lower half of the vector|<details><summary>Click here to expand the API list</summary>[vget_low_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_f32*)<br/>[vget_low_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_f64*)<br/>[vget_low_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_s16*)<br/>[vget_low_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_s32*)<br/>[vget_low_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_s64*)<br/>[vget_low_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_s8*)<br/>[vget_low_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_u16*)<br/>[vget_low_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_u32*)<br/>[vget_low_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_u64*)<br/>[vget_low_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_low_u8*)<br/></details>|

### Arithmetic

|Operation|Description|APIs|
|---|---|---|
|vadd|Add|<details><summary>Click here to expand the API list</summary>[vadd_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_f32*)<br/>[vadd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_f64*)<br/>[vadd_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_s16*)<br/>[vadd_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_s32*)<br/>[vadd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_s64*)<br/>[vadd_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_s8*)<br/>[vadd_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_u16*)<br/>[vadd_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_u32*)<br/>[vadd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_u64*)<br/>[vadd_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vadd_u8*)<br/>[vaddq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_f32*)<br/>[vaddq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_f64*)<br/>[vaddq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_s16*)<br/>[vaddq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_s32*)<br/>[vaddq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_s64*)<br/>[vaddq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_s8*)<br/>[vaddq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_u16*)<br/>[vaddq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_u32*)<br/>[vaddq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_u64*)<br/>[vaddq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddq_u8*)<br/>[vaddd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddd_s64*)<br/>[vaddd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddd_u64*)<br/></details>|
|vaddv|Add across vector|<details><summary>Click here to expand the API list</summary>[vaddv_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddv_f32*)<br/>[vaddv_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddv_s16*)<br/>[vaddv_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddv_s32*)<br/>[vaddv_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddv_s8*)<br/>[vaddv_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddv_u16*)<br/>[vaddv_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddv_u32*)<br/>[vaddv_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddv_u8*)<br/>[vaddvq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_f32*)<br/>[vaddvq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_f64*)<br/>[vaddvq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_s16*)<br/>[vaddvq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_s32*)<br/>[vaddvq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_s64*)<br/>[vaddvq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_s8*)<br/>[vaddvq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_u16*)<br/>[vaddvq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_u32*)<br/>[vaddvq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_u64*)<br/>[vaddvq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddvq_u8*)<br/></details>|
|vaddl|Add long|<details><summary>Click here to expand the API list</summary>[vaddl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_s16*)<br/>[vaddl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_s32*)<br/>[vaddl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_s8*)<br/>[vaddl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_u16*)<br/>[vaddl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_u32*)<br/>[vaddl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_u8*)<br/>[vaddl_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_high_s16*)<br/>[vaddl_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_high_s32*)<br/>[vaddl_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_high_s8*)<br/>[vaddl_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_high_u16*)<br/>[vaddl_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_high_u32*)<br/>[vaddl_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddl_high_u8*)<br/></details>|
|vaddlv|Add long across Vector|<details><summary>Click here to expand the API list</summary>[vaddlv_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlv_s16*)<br/>[vaddlv_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlv_s32*)<br/>[vaddlv_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlv_s8*)<br/>[vaddlv_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlv_u16*)<br/>[vaddlv_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlv_u32*)<br/>[vaddlv_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlv_u8*)<br/>[vaddlvq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlvq_s16*)<br/>[vaddlvq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlvq_s32*)<br/>[vaddlvq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlvq_s8*)<br/>[vaddlvq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlvq_u16*)<br/>[vaddlvq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlvq_u32*)<br/>[vaddlvq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddlvq_u8*)<br/></details>|
|vaddw|Add wide|<details><summary>Click here to expand the API list</summary>[vaddw_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_s16*)<br/>[vaddw_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_s32*)<br/>[vaddw_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_s8*)<br/>[vaddw_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_u16*)<br/>[vaddw_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_u32*)<br/>[vaddw_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_u8*)<br/>[vaddw_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_high_s16*)<br/>[vaddw_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_high_s32*)<br/>[vaddw_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_high_s8*)<br/>[vaddw_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_high_u16*)<br/>[vaddw_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_high_u32*)<br/>[vaddw_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddw_high_u8*)<br/></details>|
|vhadd|Halving add|<details><summary>Click here to expand the API list</summary>[vhadd_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhadd_s16*)<br/>[vhadd_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhadd_s32*)<br/>[vhadd_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhadd_s8*)<br/>[vhadd_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhadd_u16*)<br/>[vhadd_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhadd_u32*)<br/>[vhadd_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhadd_u8*)<br/>[vhaddq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhaddq_s16*)<br/>[vhaddq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhaddq_s32*)<br/>[vhaddq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhaddq_s8*)<br/>[vhaddq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhaddq_u16*)<br/>[vhaddq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhaddq_u32*)<br/>[vhaddq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhaddq_u8*)<br/></details>|
|vrhadd|Rounding halving add|<details><summary>Click here to expand the API list</summary>[vrhadd_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhadd_s16*)<br/>[vrhadd_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhadd_s32*)<br/>[vrhadd_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhadd_s8*)<br/>[vrhadd_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhadd_u16*)<br/>[vrhadd_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhadd_u32*)<br/>[vrhadd_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhadd_u8*)<br/>[vrhaddq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhaddq_s16*)<br/>[vrhaddq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhaddq_s32*)<br/>[vrhaddq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhaddq_s8*)<br/>[vrhaddq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhaddq_u16*)<br/>[vrhaddq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhaddq_u32*)<br/>[vrhaddq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrhaddq_u8*)<br/></details>|
|vqadd|Saturating add|<details><summary>Click here to expand the API list</summary>[vqadd_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_s16*)<br/>[vqadd_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_s32*)<br/>[vqadd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_s64*)<br/>[vqadd_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_s8*)<br/>[vqadd_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_u16*)<br/>[vqadd_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_u32*)<br/>[vqadd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_u64*)<br/>[vqadd_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadd_u8*)<br/>[vqaddq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_s16*)<br/>[vqaddq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_s32*)<br/>[vqaddq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_s64*)<br/>[vqaddq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_s8*)<br/>[vqaddq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_u16*)<br/>[vqaddq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_u32*)<br/>[vqaddq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_u64*)<br/>[vqaddq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddq_u8*)<br/>[vqaddb_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddb_s8*)<br/>[vqaddb_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddb_u8*)<br/>[vqaddh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddh_s16*)<br/>[vqaddh_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddh_u16*)<br/>[vqadds_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadds_s32*)<br/>[vqadds_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqadds_u32*)<br/>[vqaddd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddd_s64*)<br/>[vqaddd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqaddd_u64*)<br/></details>|
|vsqadd|Unsigned saturating Accumulate of signed value|<details><summary>Click here to expand the API list</summary>[vsqadd_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqadd_u16*)<br/> [vsqadd_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqadd_u32*)<br/> [vsqadd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqadd_u64*)<br/> [vsqadd_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqadd_u8*)<br/> [vsqaddq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqaddq_u16*)<br/> [vsqaddq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqaddq_u32*)<br/> [vsqaddq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqaddq_u64*)<br/> [vsqaddq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqaddq_u8*)<br/> [vsqaddb_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqaddb_u8*)<br/> [vsqaddh_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqaddh_u16*)<br/> [vsqadds_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqadds_u32*)<br/> [vsqaddd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqaddd_u64*)<br/></details>|
|vuqadd|Signed saturating Accumulate of unsigned value|<details><summary>Click here to expand the API list</summary>[vuqadd_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqadd_s16*)<br/> [vuqadd_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqadd_s32*)<br/> [vuqadd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqadd_s64*)<br/> [vuqadd_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqadd_s8*)<br/> [vuqaddq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqaddq_s16*)<br/> [vuqaddq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqaddq_s32*)<br/> [vuqaddq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqaddq_s64*)<br/> [vuqaddq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqaddq_s8*)<br/> [vuqaddb_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqaddb_s8*)<br/> [vuqaddh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqaddh_s16*)<br/> [vuqadds_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqadds_s32*)<br/> [vuqaddd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuqaddd_s64*)<br/></details>|
|vaddhn|Add returning high narrow|<details><summary>Click here to expand the API list</summary>[vaddhn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_s16*)<br/>[vaddhn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_s32*)<br/>[vaddhn_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_s64*)<br/>[vaddhn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_u16*)<br/>[vaddhn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_u32*)<br/>[vaddhn_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_u64*)<br/>[vaddhn_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_high_s16*)<br/>[vaddhn_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_high_s32*)<br/>[vaddhn_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_high_s64*)<br/>[vaddhn_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_high_u16*)<br/>[vaddhn_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_high_u32*)<br/>[vaddhn_high_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vaddhn_high_u64*)<br/></details>|
|vraddhn|Rounding add returning high narrow|<details><summary>Click here to expand the API list</summary>[vraddhn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_s16*)<br/>[vraddhn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_s32*)<br/>[vraddhn_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_s64*)<br/>[vraddhn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_u16*)<br/>[vraddhn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_u32*)<br/>[vraddhn_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_u64*)<br/>[vraddhn_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_high_s16*)<br/>[vraddhn_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_high_s32*)<br/>[vraddhn_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_high_s64*)<br/>[vraddhn_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_high_u16*)<br/>[vraddhn_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_high_u32*)<br/>[vraddhn_high_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vraddhn_high_u64*)<br/></details>|
|vpadd|Add pairwise (vector)|<details><summary>Click here to expand the API list</summary>[vpadd_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadd_f32*)<br/>[vpadd_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadd_s16*)<br/>[vpadd_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadd_s32*)<br/>[vpadd_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadd_s8*)<br/>[vpadd_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadd_u16*)<br/>[vpadd_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadd_u32*)<br/>[vpadd_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadd_u8*)<br/>[vpaddq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_f32*)<br/>[vpaddq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_f64*)<br/>[vpaddq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_s16*)<br/>[vpaddq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_s32*)<br/>[vpaddq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_s64*)<br/>[vpaddq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_s8*)<br/>[vpaddq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_u16*)<br/>[vpaddq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_u32*)<br/>[vpaddq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_u64*)<br/>[vpaddq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddq_u8*)<br/>[vpadds_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadds_f32*)<br/>[vpaddd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddd_f64*)<br/>[vpaddd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddd_s64*)<br/>[vpaddd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddd_u64*)<br/></details>|
|vpaddl|Signed add long pairwise|<details><summary>Click here to expand the API list</summary>[vpaddl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddl_s16*)<br/>[vpaddl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddl_s32*)<br/>[vpaddl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddl_s8*)<br/>[vpaddl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddl_u16*)<br/>[vpaddl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddl_u32*)<br/>[vpaddl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddl_u8*)<br/>[vpaddlq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddlq_s16*)<br/>[vpaddlq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddlq_s32*)<br/>[vpaddlq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddlq_s8*)<br/>[vpaddlq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddlq_u16*)<br/>[vpaddlq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddlq_u32*)<br/>[vpaddlq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpaddlq_u8*)<br/></details>|
|vpadal|Signed add and accumulate long pairwise|<details><summary>Click here to expand the API list</summary>[vpadal_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadal_s16*)<br/>[vpadal_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadal_s32*)<br/>[vpadal_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadal_s8*)<br/>[vpadal_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadal_u16*)<br/>[vpadal_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadal_u32*)<br/>[vpadal_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadal_u8*)<br/>[vpadalq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadalq_s16*)<br/>[vpadalq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadalq_s32*)<br/>[vpadalq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadalq_s8*)<br/>[vpadalq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadalq_u16*)<br/>[vpadalq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadalq_u32*)<br/>[vpadalq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpadalq_u8*)<br/></details>|
|vsub|Subtract|<details><summary>Click here to expand the API list</summary>[vsub_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_f32*)<br/>[vsub_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_f64*)<br/>[vsub_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_s16*)<br/>[vsub_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_s32*)<br/>[vsub_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_s64*)<br/>[vsub_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_s8*)<br/>[vsub_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_u16*)<br/>[vsub_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_u32*)<br/>[vsub_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_u64*)<br/>[vsub_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsub_u8*)<br/>[vsubq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_f32*)<br/>[vsubq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_f64*)<br/>[vsubq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_s16*)<br/>[vsubq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_s32*)<br/>[vsubq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_s64*)<br/>[vsubq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_s8*)<br/>[vsubq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_u16*)<br/>[vsubq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_u32*)<br/>[vsubq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_u64*)<br/>[vsubq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubq_u8*)<br/>[vsubd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubd_s64*)<br/>[vsubd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubd_u64*)<br/></details>|
|vsubl|Subtract long|<details><summary>Click here to expand the API list</summary>[vsubl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_s16*)<br/>[vsubl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_s32*)<br/>[vsubl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_s8*)<br/>[vsubl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_u16*)<br/>[vsubl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_u32*)<br/>[vsubl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_u8*)<br/>[vsubl_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_high_s16*)<br/>[vsubl_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_high_s32*)<br/>[vsubl_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_high_s8*)<br/>[vsubl_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_high_u16*)<br/>[vsubl_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_high_u32*)<br/>[vsubl_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubl_high_u8*)<br/></details>|
|vsubw|Subtract wide|<details><summary>Click here to expand the API list</summary>[vsubw_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_s16*)<br/>[vsubw_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_s32*)<br/>[vsubw_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_s8*)<br/>[vsubw_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_u16*)<br/>[vsubw_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_u32*)<br/>[vsubw_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_u8*)<br/>[vsubw_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_high_s16*)<br/>[vsubw_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_high_s32*)<br/>[vsubw_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_high_s8*)<br/>[vsubw_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_high_u16*)<br/>[vsubw_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_high_u32*)<br/>[vsubw_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubw_high_u8*)<br/></details>|
|vhsub|Halving subtract|<details><summary>Click here to expand the API list</summary>[vhsub_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsub_s16*)<br/>[vhsub_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsub_s32*)<br/>[vhsub_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsub_s8*)<br/>[vhsub_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsub_u16*)<br/>[vhsub_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsub_u32*)<br/>[vhsub_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsub_u8*)<br/>[vhsubq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsubq_s16*)<br/>[vhsubq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsubq_s32*)<br/>[vhsubq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsubq_s8*)<br/>[vhsubq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsubq_u16*)<br/>[vhsubq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsubq_u32*)<br/>[vhsubq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vhsubq_u8*)<br/></details>|
|vqsub|Saturating subtract|<details><summary>Click here to expand the API list</summary>[vqsub_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_s16*)<br/> [vqsub_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_s32*)<br/> [vqsub_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_s64*)<br/> [vqsub_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_s8*)<br/> [vqsub_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_u16*)<br/> [vqsub_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_u32*)<br/> [vqsub_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_u64*)<br/> [vqsub_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsub_u8*)<br/> [vqsubq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_s16*)<br/> [vqsubq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_s32*)<br/> [vqsubq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_s64*)<br/> [vqsubq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_s8*)<br/> [vqsubq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_u16*)<br/> [vqsubq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_u32*)<br/> [vqsubq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_u64*)<br/> [vqsubq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubq_u8*)<br/> [vqsubb_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubb_s8*)<br/> [vqsubb_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubb_u8*)<br/> [vqsubh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubh_s16*)<br/> [vqsubh_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubh_u16*)<br/> [vqsubs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubs_s32*)<br/> [vqsubs_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubs_u32*)<br/> [vqsubd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubd_s64*)<br/> [vqsubd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqsubd_u64*)<br/></details>|
|vsubhn|Subtract returning high narrow|<details><summary>Click here to expand the API list</summary>[vsubhn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_s16*)<br/>[vsubhn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_s32*)<br/>[vsubhn_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_s64*)<br/>[vsubhn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_u16*)<br/>[vsubhn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_u32*)<br/>[vsubhn_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_u64*)<br/>[vsubhn_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_high_s16*)<br/>[vsubhn_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_high_s32*)<br/>[vsubhn_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_high_s64*)<br/>[vsubhn_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_high_u16*)<br/>[vsubhn_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_high_u32*)<br/>[vsubhn_high_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsubhn_high_u64*)<br/></details>|
|vrsubhn|Rounding subtract returning high narrow|<details><summary>Click here to expand the API list</summary>[vrsubhn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_s16*)<br/>[vrsubhn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_s32*)<br/>[vrsubhn_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_s64*)<br/>[vrsubhn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_u16*)<br/>[vrsubhn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_u32*)<br/>[vrsubhn_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_u64*)<br/>[vrsubhn_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_high_s16*)<br/>[vrsubhn_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_high_s32*)<br/>[vrsubhn_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_high_s64*)<br/>[vrsubhn_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_high_u16*)<br/>[vrsubhn_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_high_u32*)<br/>[vrsubhn_high_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsubhn_high_u64*)<br/></details>|

### Multiply

|Operation|Description|APIs|
|---|---|---|
|vmul|Multiply (vector)|<details><summary>Click here to expand the API list</summary>[vmul_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_f32*)<br/>[vmul_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_f64*)<br/>[vmul_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_s16*)<br/>[vmul_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_s32*)<br/>[vmul_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_s8*)<br/>[vmul_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_u16*)<br/>[vmul_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_u32*)<br/>[vmul_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_u8*)<br/>[vmulq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_f32*)<br/>[vmulq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_f64*)<br/>[vmulq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_s16*)<br/>[vmulq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_s32*)<br/>[vmulq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_s8*)<br/>[vmulq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_u16*)<br/>[vmulq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_u32*)<br/>[vmulq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_u8*)<br/></details>|
|vmul_n|Vector multiply by scalar|<details><summary>Click here to expand the API list</summary>[vmul_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_n_f32*)<br/>[vmul_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_n_f64*)<br/>[vmul_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_n_s16*)<br/>[vmul_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_n_s32*)<br/>[vmul_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_n_u16*)<br/>[vmul_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_n_u32*)<br/>[vmulq_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_n_f32*)<br/>[vmulq_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_n_f64*)<br/>[vmulq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_n_s16*)<br/>[vmulq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_n_s32*)<br/>[vmulq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_n_u16*)<br/>[vmulq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_n_u32*)<br/></details>|
|vmul_lane|Multiply (vector)|<details><summary>Click here to expand the API list</summary>[vmul_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_lane_f32*)<br/>[vmul_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_lane_f64*)<br/>[vmul_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_lane_s16*)<br/>[vmul_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_lane_s32*)<br/>[vmul_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_lane_u16*)<br/>[vmul_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_lane_u32*)<br/>[vmul_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_laneq_f32*)<br/>[vmul_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_laneq_f64*)<br/>[vmul_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_laneq_s16*)<br/>[vmul_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_laneq_s32*)<br/>[vmul_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_laneq_u16*)<br/>[vmul_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmul_laneq_u32*)<br/>[vmulq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_lane_f32*)<br/>[vmulq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_lane_f64*)<br/>[vmulq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_lane_s16*)<br/>[vmulq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_lane_s32*)<br/>[vmulq_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_lane_u16*)<br/>[vmulq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_lane_u32*)<br/>[vmulq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_laneq_f32*)<br/>[vmulq_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_laneq_f64*)<br/>[vmulq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_laneq_s16*)<br/>[vmulq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_laneq_s32*)<br/>[vmulq_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_laneq_u16*)<br/>[vmulq_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulq_laneq_u32*)<br/>[vmuls_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmuls_lane_f32*)<br/>[vmuls_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmuls_laneq_f32*)<br/>[vmuld_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmuld_lane_f64*)<br/>[vmuld_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmuld_laneq_f64*)<br/></details>|
|vmull|Multiply long (vector)|<details><summary>Click here to expand the API list</summary>[vmull_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_s16*)<br/>[vmull_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_s32*)<br/>[vmull_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_s8*)<br/>[vmull_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_u16*)<br/>[vmull_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_u32*)<br/>[vmull_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_u8*)<br/>[vmull_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_s16*)<br/>[vmull_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_s32*)<br/>[vmull_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_s8*)<br/>[vmull_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_u16*)<br/>[vmull_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_u32*)<br/>[vmull_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_u8*)<br/></details>|
|vmull_n|Vector long multiply by scalar|<details><summary>Click here to expand the API list</summary>[vmull_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_n_s16*)<br/>[vmull_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_n_s32*)<br/>[vmull_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_n_u16*)<br/>[vmull_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_n_u32*)<br/>[vmull_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_n_s16*)<br/>[vmull_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_n_s32*)<br/>[vmull_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_n_u16*)<br/>[vmull_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_n_u32*)<br/></details>|
|vmull_lane|Multiply long (vector)|<details><summary>Click here to expand the API list</summary>[vmull_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_lane_s16*)<br/>[vmull_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_lane_s32*)<br/>[vmull_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_lane_u16*)<br/>[vmull_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_lane_u32*)<br/>[vmull_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_laneq_s16*)<br/>[vmull_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_laneq_s32*)<br/>[vmull_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_laneq_u16*)<br/>[vmull_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_laneq_u32*)<br/>[vmull_high_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_lane_s16*)<br/>[vmull_high_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_lane_s32*)<br/>[vmull_high_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_lane_u16*)<br/>[vmull_high_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_lane_u32*)<br/>[vmull_high_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_laneq_s16*)<br/>[vmull_high_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_laneq_s32*)<br/>[vmull_high_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_laneq_u16*)<br/>[vmull_high_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmull_high_laneq_u32*)<br/></details>|
|vmulx|Floating-point multiply extended|<details><summary>Click here to expand the API list</summary>[vmulx_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulx_f32*)<br/>[vmulx_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulx_f64*)<br/>[vmulx_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulx_lane_f32*)<br/>[vmulx_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulx_lane_f64*)<br/>[vmulx_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulx_laneq_f32*)<br/>[vmulx_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulx_laneq_f64*)<br/>[vmulxq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxq_f32*)<br/>[vmulxq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxq_f64*)<br/>[vmulxq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxq_lane_f32*)<br/>[vmulxq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxq_lane_f64*)<br/>[vmulxq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxq_laneq_f32*)<br/>[vmulxq_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxq_laneq_f64*)<br/>[vmulxs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxs_f32*)<br/>[vmulxs_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxs_lane_f32*)<br/>[vmulxs_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxs_laneq_f32*)<br/>[vmulxd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxd_f64*)<br/>[vmulxd_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxd_lane_f64*)<br/>[vmulxd_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmulxd_laneq_f64*)<br/></details>|
|vmla|Multiply-add to accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vmla_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_f32*)<br/>[vmla_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_f64*)<br/>[vmla_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_s16*)<br/>[vmla_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_s32*)<br/>[vmla_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_s8*)<br/>[vmla_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_u16*)<br/>[vmla_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_u32*)<br/>[vmla_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_u8*)<br/>[vmlaq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_f32*)<br/>[vmlaq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_f64*)<br/>[vmlaq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_s16*)<br/>[vmlaq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_s32*)<br/>[vmlaq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_s8*)<br/>[vmlaq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_u16*)<br/>[vmlaq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_u32*)<br/>[vmlaq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_u8*)<br/></details>|
|vmla_lane|Vector multiply accumulate with scalar|<details><summary>Click here to expand the API list</summary>[vmla_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_lane_f32*)<br/>[vmla_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_lane_s16*)<br/>[vmla_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_lane_s32*)<br/>[vmla_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_lane_u16*)<br/>[vmla_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_lane_u32*)<br/>[vmla_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_laneq_f32*)<br/>[vmla_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_laneq_s16*)<br/>[vmla_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_laneq_s32*)<br/>[vmla_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_laneq_u16*)<br/>[vmla_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_laneq_u32*)<br/>[vmlaq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_lane_f32*)<br/>[vmlaq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_lane_s16*)<br/>[vmlaq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_lane_s32*)<br/>[vmlaq_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_lane_u16*)<br/>[vmlaq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_lane_u32*)<br/>[vmlaq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_laneq_f32*)<br/>[vmlaq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_laneq_s16*)<br/>[vmlaq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_laneq_s32*)<br/>[vmlaq_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_laneq_u16*)<br/>[vmlaq_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_laneq_u32*)<br/></details>|
|vmla_n|Vector multiply accumulate with scalar|<details><summary>Click here to expand the API list</summary>[vmla_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_n_f32*)<br/>[vmla_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_n_s16*)<br/>[vmla_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_n_s32*)<br/>[vmla_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_n_u16*)<br/>[vmla_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmla_n_u32*)<br/>[vmlaq_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_n_f32*)<br/>[vmlaq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_n_s16*)<br/>[vmlaq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_n_s32*)<br/>[vmlaq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_n_u16*)<br/>[vmlaq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlaq_n_u32*)<br/></details>|
|vmlal|Multiply-accumulate long (vector)|<details><summary>Click here to expand the API list</summary>[vmlal_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_s16*)<br/>[vmlal_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_s32*)<br/>[vmlal_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_s8*)<br/>[vmlal_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_u16*)<br/>[vmlal_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_u32*)<br/>[vmlal_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_u8*)<br/>[vmlal_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_s16*)<br/>[vmlal_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_s32*)<br/>[vmlal_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_s8*)<br/>[vmlal_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_u16*)<br/>[vmlal_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_u32*)<br/>[vmlal_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_u8*)<br/></details>|
|vmlal_lane|Multiply-accumulate long with scalar|<details><summary>Click here to expand the API list</summary>[vmlal_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_lane_s16*)<br/>[vmlal_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_lane_s32*)<br/>[vmlal_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_lane_u16*)<br/>[vmlal_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_lane_u32*)<br/>[vmlal_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_laneq_s16*)<br/>[vmlal_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_laneq_s32*)<br/>[vmlal_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_laneq_u16*)<br/>[vmlal_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_laneq_u32*)<br/>[vmlal_high_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_lane_s16*)<br/>[vmlal_high_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_lane_s32*)<br/>[vmlal_high_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_lane_u16*)<br/>[vmlal_high_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_lane_u32*)<br/>[vmlal_high_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_laneq_s16*)<br/>[vmlal_high_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_laneq_s32*)<br/>[vmlal_high_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_laneq_u16*)<br/>[vmlal_high_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_laneq_u32*)<br/></details>|
|vmlal_n|Multiply-accumulate long with scalar|<details><summary>Click here to expand the API list</summary>[vmlal_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_n_s16*)<br/>[vmlal_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_n_s32*)<br/>[vmlal_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_n_u16*)<br/>[vmlal_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_n_u32*)<br/>[vmlal_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_n_s16*)<br/>[vmlal_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_n_s32*)<br/>[vmlal_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_n_u16*)<br/>[vmlal_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlal_high_n_u32*)<br/></details>|
|vmls|Multiply-subtract from accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vmls_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_f32*)<br/>[vmls_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_f64*)<br/>[vmls_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_s16*)<br/>[vmls_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_s32*)<br/>[vmls_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_s8*)<br/>[vmls_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_u16*)<br/>[vmls_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_u32*)<br/>[vmls_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_u8*)<br/>[vmlsq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_f32*)<br/>[vmlsq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_f64*)<br/>[vmlsq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_s16*)<br/>[vmlsq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_s32*)<br/>[vmlsq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_s8*)<br/>[vmlsq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_u16*)<br/>[vmlsq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_u32*)<br/>[vmlsq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_u8*)<br/></details>|
|vmls_lane|Vector multiply subtract with scalar|<details><summary>Click here to expand the API list</summary>[vmls_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_lane_f32*)<br/>[vmls_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_lane_s16*)<br/>[vmls_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_lane_s32*)<br/>[vmls_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_lane_u16*)<br/>[vmls_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_lane_u32*)<br/>[vmls_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_laneq_f32*)<br/>[vmls_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_laneq_s16*)<br/>[vmls_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_laneq_s32*)<br/>[vmls_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_laneq_u16*)<br/>[vmls_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_laneq_u32*)<br/>[vmlsq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_lane_f32*)<br/>[vmlsq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_lane_s16*)<br/>[vmlsq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_lane_s32*)<br/>[vmlsq_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_lane_u16*)<br/>[vmlsq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_lane_u32*)<br/>[vmlsq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_laneq_f32*)<br/>[vmlsq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_laneq_s16*)<br/>[vmlsq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_laneq_s32*)<br/>[vmlsq_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_laneq_u16*)<br/>[vmlsq_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_laneq_u32*)<br/></details>|
|vmls_n|Vector multiply subtract with scalar|<details><summary>Click here to expand the API list</summary>[vmls_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_n_f32*)<br/>[vmls_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_n_s16*)<br/>[vmls_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_n_s32*)<br/>[vmls_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_n_u16*)<br/>[vmls_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmls_n_u32*)<br/>[vmlsq_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_n_f32*)<br/>[vmlsq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_n_s16*)<br/>[vmlsq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_n_s32*)<br/>[vmlsq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_n_u16*)<br/>[vmlsq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsq_n_u32*)<br/></details>|
|vmlsl|Multiply-subtract long (vector)|<details><summary>Click here to expand the API list</summary>[vmlsl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_s16*)<br/>[vmlsl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_s32*)<br/>[vmlsl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_s8*)<br/>[vmlsl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_u16*)<br/>[vmlsl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_u32*)<br/>[vmlsl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_u8*)<br/>[vmlsl_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_s16*)<br/>[vmlsl_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_s32*)<br/>[vmlsl_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_s8*)<br/>[vmlsl_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_u16*)<br/>[vmlsl_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_u32*)<br/>[vmlsl_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_u8*)<br/></details>|
|vmlsl_lane|Vector multiply-subtract long with scalar|<details><summary>Click here to expand the API list</summary>[vmlsl_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_lane_s16*)<br/>[vmlsl_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_lane_s32*)<br/>[vmlsl_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_lane_u16*)<br/>[vmlsl_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_lane_u32*)<br/>[vmlsl_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_laneq_s16*)<br/>[vmlsl_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_laneq_s32*)<br/>[vmlsl_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_laneq_u16*)<br/>[vmlsl_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_laneq_u32*)<br/>[vmlsl_high_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_lane_s16*)<br/>[vmlsl_high_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_lane_s32*)<br/>[vmlsl_high_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_lane_u16*)<br/>[vmlsl_high_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_lane_u32*)<br/>[vmlsl_high_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_laneq_s16*)<br/>[vmlsl_high_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_laneq_s32*)<br/>[vmlsl_high_laneq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_laneq_u16*)<br/>[vmlsl_high_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_laneq_u32*)<br/></details>|
|vmlsl_n|Vector multiply-subtract long with scalar|<details><summary>Click here to expand the API list</summary>[vmlsl_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_n_s16*)<br/>[vmlsl_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_n_s32*)<br/>[vmlsl_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_n_u16*)<br/>[vmlsl_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_n_u32*)<br/>[vmlsl_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_n_s16*)<br/>[vmlsl_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_n_s32*)<br/>[vmlsl_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_n_u16*)<br/>[vmlsl_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmlsl_high_n_u32*)<br/></details>|
|vqdmull|Signed saturating doubling multiply long|<details><summary>Click here to expand the API list</summary>[vqdmull_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_s16*)<br/>[vqdmull_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_s32*)<br/>[vqdmullh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmullh_s16*)<br/>[vqdmulls_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulls_s32*)<br/>[vqdmull_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_s16*)<br/>[vqdmull_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_s32*)<br/></details>|
|vqdmull_lane|Vector saturating doubling multiply long with scalar|<details><summary>Click here to expand the API list</summary>[vqdmull_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_lane_s16*)<br/>[vqdmull_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_lane_s32*)<br/>[vqdmull_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_laneq_s16*)<br/>[vqdmull_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_laneq_s32*)<br/>[vqdmullh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmullh_lane_s16*)<br/>[vqdmullh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmullh_laneq_s16*)<br/>[vqdmulls_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulls_lane_s32*)<br/>[vqdmulls_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulls_laneq_s32*)<br/>[vqdmull_high_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_lane_s16*)<br/>[vqdmull_high_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_lane_s32*)<br/>[vqdmull_high_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_laneq_s16*)<br/>[vqdmull_high_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_laneq_s32*)<br/></details>|
|vqdmull_n|Vector saturating doubling multiply long with scalar|<details><summary>Click here to expand the API list</summary>[vqdmull_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_n_s16*)<br/>[vqdmull_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_n_s32*)<br/>[vqdmull_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_n_s16*)<br/>[vqdmull_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmull_high_n_s32*)<br/></details>|
|vqdmulh|Saturating doubling multiply returning high half|<details><summary>Click here to expand the API list</summary>[vqdmulh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_s16*)<br/>[vqdmulh_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_s32*)<br/>[vqdmulhq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_s16*)<br/>[vqdmulhq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_s32*)<br/>[vqdmulhh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhh_s16*)<br/>[vqdmulhs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhs_s32*)<br/></details>|
|vqdmulh_lane|Vector saturating doubling multiply high by scalar|<details><summary>Click here to expand the API list</summary>[vqdmulh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_lane_s16*)<br/>[vqdmulh_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_lane_s32*)<br/>[vqdmulh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_laneq_s16*)<br/>[vqdmulh_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_laneq_s32*)<br/>[vqdmulhq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_lane_s16*)<br/>[vqdmulhq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_lane_s32*)<br/>[vqdmulhq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_laneq_s16*)<br/>[vqdmulhq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_laneq_s32*)<br/>[vqdmulhh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhh_lane_s16*)<br/>[vqdmulhh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhh_laneq_s16*)<br/>[vqdmulhs_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhs_lane_s32*)<br/>[vqdmulhs_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhs_laneq_s32*)<br/></details>|
|vqdmulh_n|Vector saturating doubling multiply high by scalar|<details><summary>Click here to expand the API list</summary>[vqdmulh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_n_s16*)<br/>[vqdmulh_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulh_n_s32*)<br/>[vqdmulhq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_n_s16*)<br/>[vqdmulhq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmulhq_n_s32*)<br/></details>|
|vqrdmulh|Saturating rounding doubling multiply returning high half|<details><summary>Click here to expand the API list</summary>[vqrdmulh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_s16*)<br/>[vqrdmulh_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_s32*)<br/>[vqrdmulhq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_s16*)<br/>[vqrdmulhq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_s32*)<br/>[vqrdmulhh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhh_s16*)<br/>[vqrdmulhs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhs_s32*)<br/></details>|
|vqrdmulh_lane|Vector saturating rounding doubling multiply high with scalar|<details><summary>Click here to expand the API list</summary>[vqrdmulh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_lane_s16*)<br/>[vqrdmulh_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_lane_s32*)<br/>[vqrdmulh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_laneq_s16*)<br/>[vqrdmulh_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_laneq_s32*)<br/>[vqrdmulhq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_lane_s16*)<br/>[vqrdmulhq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_lane_s32*)<br/>[vqrdmulhq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_laneq_s16*)<br/>[vqrdmulhq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_laneq_s32*)<br/>[vqrdmulhh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhh_lane_s16*)<br/>[vqrdmulhh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhh_laneq_s16*)<br/>[vqrdmulhs_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhs_lane_s32*)<br/>[vqrdmulhs_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhs_laneq_s32*)<br/></details>|
|vqrdmulh_n|Vector saturating rounding doubling multiply high with scalar|<details><summary>Click here to expand the API list</summary>[vqrdmulh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_n_s16*)<br/>[vqrdmulh_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulh_n_s32*)<br/>[vqrdmulhq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_n_s16*)<br/>[vqrdmulhq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmulhq_n_s32*)<br/></details>|
|vqdmlal|Saturating doubling multiply-add long|<details><summary>Click here to expand the API list</summary>[vqdmlal_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_s16*)<br/>[vqdmlal_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_s32*)<br/>[vqdmlalh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlalh_s16*)<br/>[vqdmlals_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlals_s32*)<br/>[vqdmlal_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_s16*)<br/>[vqdmlal_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_s32*)<br/></details>|
|vqdmlal_lane|Vector saturating doubling multiply-accumulate long<br/> with scalar|<details><summary>Click here to expand the API list</summary>[vqdmlal_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_lane_s16*)<br/>[vqdmlal_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_lane_s32*)<br/>[vqdmlal_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_laneq_s16*)<br/>[vqdmlal_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_laneq_s32*)<br/>[vqdmlalh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlalh_lane_s16*)<br/>[vqdmlalh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlalh_laneq_s16*)<br/>[vqdmlals_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlals_lane_s32*)<br/>[vqdmlals_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlals_laneq_s32*)<br/>[vqdmlal_high_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_lane_s16*)<br/>[vqdmlal_high_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_lane_s32*)<br/>[vqdmlal_high_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_laneq_s16*)<br/>[vqdmlal_high_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_laneq_s32*)<br/></details>|
|vqdmlal_n|Vector saturating doubling multiply-accumulate long<br/> with scalar|<details><summary>Click here to expand the API list</summary>[vqdmlal_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_n_s16*)<br/>[vqdmlal_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_n_s32*)<br/>[vqdmlal_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_n_s16*)<br/>[vqdmlal_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlal_high_n_s32*)<br/></details>|
|vqdmlsl|Signed saturating doubling multiply-subtract long|<details><summary>Click here to expand the API list</summary>[vqdmlsl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_s16*)<br/>[vqdmlsl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_s32*)<br/>[vqdmlslh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlslh_s16*)<br/>[vqdmlsls_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsls_s32*)<br/>[vqdmlsl_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_s16*)<br/>[vqdmlsl_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_s32*)<br/></details>|
|vqdmlsl_lane|Vector saturating doubling multiply-subtract long<br/> with scalar|<details><summary>Click here to expand the API list</summary>[vqdmlsl_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_lane_s16*)<br/>[vqdmlsl_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_lane_s32*)<br/>[vqdmlsl_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_laneq_s16*)<br/>[vqdmlsl_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_laneq_s32*)<br/>[vqdmlslh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlslh_lane_s16*)<br/>[vqdmlslh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlslh_laneq_s16*)<br/>[vqdmlsls_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsls_lane_s32*)<br/>[vqdmlsls_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsls_laneq_s32*)<br/>[vqdmlsl_high_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_lane_s16*)<br/>[vqdmlsl_high_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_lane_s32*)<br/>[vqdmlsl_high_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_laneq_s16*)<br/>[vqdmlsl_high_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_laneq_s32*)<br/></details>|
|vqdmlsl_n|Vector saturating doubling multiply-subtract long<br/> with scalar|<details><summary>Click here to expand the API list</summary>[vqdmlsl_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_n_s16*)<br/>[vqdmlsl_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_n_s32*)<br/>[vqdmlsl_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_n_s16*)<br/>[vqdmlsl_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqdmlsl_high_n_s32*)<br/></details>|
|vqrdmlah|Saturating rounding doubling multiply accumulate<br/> returning high half (vector)|<details><summary>Click here to expand the API list</summary>[vqrdmlah_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlah_s16*)<br/>[vqrdmlah_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlah_s32*)<br/>[vqrdmlahq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahq_s16*)<br/>[vqrdmlahq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahq_s32*)<br/>[vqrdmlahh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahh_s16*)<br/>[vqrdmlahs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahs_s32*)<br/></details>|
|vqrdmlah_lane|Saturating rounding doubling multiply accumulate<br/> returning high half (vector)|<details><summary>Click here to expand the API list</summary>[vqrdmlah_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlah_lane_s16*)<br/> [vqrdmlah_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlah_lane_s32*)<br/> [vqrdmlah_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlah_laneq_s16*)<br/> [vqrdmlah_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlah_laneq_s32*)<br/> [vqrdmlahq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahq_lane_s16*)<br/> [vqrdmlahq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahq_lane_s32*)<br/> [vqrdmlahq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahq_laneq_s16*)<br/> [vqrdmlahq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahq_laneq_s32*)<br/> [vqrdmlahh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahh_lane_s16*)<br/> [vqrdmlahh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahh_laneq_s16*)<br/> [vqrdmlahs_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlahs_lane_s32*)<br/></details>|
|vqrdmlsh|Saturating rounding doubling multiply subtract<br/> returning high half (vector)|<details><summary>Click here to expand the API list</summary>[vqrdmlsh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlsh_s16*)<br/>[vqrdmlsh_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlsh_s32*)<br/>[vqrdmlshq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshq_s16*)<br/>[vqrdmlshq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshq_s32*)<br/>[vqrdmlshh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshh_s16*)<br/>[vqrdmlshs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshs_s32*)<br/></details>|
|vqrdmlsh_lane|Saturating rounding doubling multiply subtract<br/> returning high half (vector)|<details><summary>Click here to expand the API list</summary>[vqrdmlsh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlsh_lane_s16*)<br/>[vqrdmlsh_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlsh_lane_s32*)<br/>[vqrdmlsh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlsh_laneq_s16*)<br/>[vqrdmlsh_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlsh_laneq_s32*)<br/>[vqrdmlshq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshq_lane_s16*)<br/>[vqrdmlshq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshq_lane_s32*)<br/>[vqrdmlshq_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshq_laneq_s16*)<br/>[vqrdmlshq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshq_laneq_s32*)<br/>[vqrdmlshh_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshh_lane_s16*)<br/>[vqrdmlshh_laneq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshh_laneq_s16*)<br/>[vqrdmlshs_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrdmlshs_lane_s32*)<br/></details>|
|vfma|Floating-point fused multiply-add to accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vfma_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_f32*)<br/>[vfma_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_f64*)<br/>[vfmaq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_f32*)<br/>[vfmaq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_f64*)<br/></details>|
|vfma_n|Floating-point fused multiply-add to accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vfma_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_n_f32*)<br/>[vfma_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_n_f64*)<br/>[vfmaq_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_n_f32*)<br/>[vfmaq_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_n_f64*)<br/></details>|
|vfma_lane|Floating-point fused multiply-add to accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vfma_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_lane_f32*)<br/>[vfma_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_lane_f64*)<br/>[vfma_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_laneq_f32*)<br/>[vfma_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfma_laneq_f64*)<br/>[vfmaq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_lane_f32*)<br/>[vfmaq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_lane_f64*)<br/>[vfmaq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_laneq_f32*)<br/>[vfmaq_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmaq_laneq_f64*)<br/>[vfmas_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmas_lane_f32*)<br/>[vfmas_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmas_laneq_f32*)<br/>[vfmad_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmad_lane_f64*)<br/>[vfmad_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmad_laneq_f64*)<br/></details>|
|vfms|Floating-point fused multiply-subtract<br/> from accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vfms_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_f32*)<br/>[vfms_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_f64*)<br/>[vfmsq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_f32*)<br/>[vfmsq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_f64*)<br/></details>|
|vfms_n|Floating-point fused multiply-subtract<br/> from accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vfms_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_n_f32*)<br/>[vfms_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_n_f64*)<br/>[vfmsq_n_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_n_f32*)<br/>[vfmsq_n_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_n_f64*)<br/></details>|
|vfms_lane|Floating-point fused multiply-subtract<br/> from accumulator (vector)|<details><summary>Click here to expand the API list</summary>[vfms_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_lane_f32*)<br/>[vfms_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_lane_f64*)<br/>[vfms_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_laneq_f32*)<br/>[vfms_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfms_laneq_f64*)<br/>[vfmsd_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsd_lane_f64*)<br/>[vfmsd_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsd_laneq_f64*)<br/>[vfmsq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_lane_f32*)<br/>[vfmsq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_lane_f64*)<br/>[vfmsq_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_laneq_f32*)<br/>[vfmsq_laneq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmsq_laneq_f64*)<br/>[vfmss_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmss_lane_f32*)<br/>[vfmss_laneq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vfmss_laneq_f32*)<br/></details>|
|vdiv|Floating-point divide (vector)|<details><summary>Click here to expand the API list</summary>[vdiv_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdiv_f32*)<br/> [vdiv_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdiv_f64*)<br/> [vdivq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdivq_f32*)<br/> [vdivq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vdivq_f64*)<br/></details>|

### Data processing

|Operation|Description|APIs|
|---|---|---|
|vpmax|Maximum pairwise|<details><summary>Click here to expand the API list</summary>[vpmax_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmax_f32*)<br/>[vpmax_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmax_s16*)<br/>[vpmax_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmax_s32*)<br/>[vpmax_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmax_s8*)<br/>[vpmax_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmax_u16*)<br/>[vpmax_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmax_u32*)<br/>[vpmax_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmax_u8*)<br/>[vpmaxq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_f32*)<br/>[vpmaxq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_f64*)<br/>[vpmaxq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_s16*)<br/>[vpmaxq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_s32*)<br/>[vpmaxq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_s8*)<br/>[vpmaxq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_u16*)<br/>[vpmaxq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_u32*)<br/>[vpmaxq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxq_u8*)<br/>[vpmaxs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxs_f32*)<br/>[vpmaxqd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxqd_f64*)<br/></details>|
|vpmaxnm|Floating-point maximum number pairwise (vector)|<details><summary>Click here to expand the API list</summary>[vpmaxnm_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxnm_f32*)<br/>[vpmaxnmq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxnmq_f32*)<br/>[vpmaxnmq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxnmq_f64*)<br/>[vpmaxnms_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxnms_f32*)<br/>[vpmaxnmqd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmaxnmqd_f64*)<br/></details>|
|vpmin|Minimum pairwise|<details><summary>Click here to expand the API list</summary>[vpmin_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmin_f32*)<br/>[vpmin_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmin_s16*)<br/>[vpmin_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmin_s32*)<br/>[vpmin_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmin_s8*)<br/>[vpmin_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmin_u16*)<br/>[vpmin_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmin_u32*)<br/>[vpmin_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmin_u8*)<br/>[vpminq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_f32*)<br/>[vpminq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_f64*)<br/>[vpminq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_s16*)<br/>[vpminq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_s32*)<br/>[vpminq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_s8*)<br/>[vpminq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_u16*)<br/>[vpminq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_u32*)<br/>[vpminq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminq_u8*)<br/>[vpmins_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpmins_f32*)<br/>[vpminqd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminqd_f64*)<br/></details>|
|vpminnm|Floating-point minimum number pairwise (vector)|<details><summary>Click here to expand the API list</summary>[vpminnm_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminnm_f32*)<br/>[vpminnmq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminnmq_f32*)<br/>[vpminnmq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminnmq_f64*)<br/>[vpminnms_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminnms_f32*)<br/>[vpminnmqd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vpminnmqd_f64*)<br/></details>|
|vabd|Absolute difference|<details><summary>Click here to expand the API list</summary>[vabd_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_f32*)<br/>[vabd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_f64*)<br/>[vabd_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_s16*)<br/>[vabd_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_s32*)<br/>[vabd_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_s8*)<br/>[vabd_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_u16*)<br/>[vabd_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_u32*)<br/>[vabd_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabd_u8*)<br/>[vabdq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_f32*)<br/>[vabdq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_f64*)<br/>[vabdq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_s16*)<br/>[vabdq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_s32*)<br/>[vabdq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_s8*)<br/>[vabdq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_u16*)<br/>[vabdq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_u32*)<br/>[vabdq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdq_u8*)<br/>[vabds_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabds_f32*)<br/>[vabdd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdd_f64*)<br/></details>|
|vabdl|Absolute difference long|<details><summary>Click here to expand the API list</summary>[vabdl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_s16*)<br/>[vabdl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_s32*)<br/>[vabdl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_s8*)<br/>[vabdl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_u16*)<br/>[vabdl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_u32*)<br/>[vabdl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_u8*)<br/>[vabdl_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_high_s16*)<br/>[vabdl_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_high_s32*)<br/>[vabdl_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_high_s8*)<br/>[vabdl_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_high_u16*)<br/>[vabdl_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_high_u32*)<br/>[vabdl_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabdl_high_u8*)<br/></details>|
|vaba|Absolute difference and accumulate|<details><summary>Click here to expand the API list</summary>[vaba_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaba_s16*)<br/>[vaba_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaba_s32*)<br/>[vaba_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaba_s8*)<br/>[vaba_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vaba_u16*)<br/>[vaba_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vaba_u32*)<br/>[vaba_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaba_u8*)<br/>[vabaq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabaq_s16*)<br/>[vabaq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabaq_s32*)<br/>[vabaq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabaq_s8*)<br/>[vabaq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabaq_u16*)<br/>[vabaq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabaq_u32*)<br/>[vabaq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabaq_u8*)<br/></details>|
|vabal|Absolute difference and accumulate long|<details><summary>Click here to expand the API list</summary>[vabal_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_s16*)<br/>[vabal_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_s32*)<br/>[vabal_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_s8*)<br/>[vabal_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_u16*)<br/>[vabal_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_u32*)<br/>[vabal_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_u8*)<br/>[vabal_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_high_s16*)<br/>[vabal_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_high_s32*)<br/>[vabal_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_high_s8*)<br/>[vabal_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_high_u16*)<br/>[vabal_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_high_u32*)<br/>[vabal_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabal_high_u8*)<br/></details>|
|vmax|Maximum|<details><summary>Click here to expand the API list</summary>[vmax_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_f32*)<br/>[vmax_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_f64*)<br/>[vmax_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_s16*)<br/>[vmax_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_s32*)<br/>[vmax_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_s8*)<br/>[vmax_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_u16*)<br/>[vmax_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_u32*)<br/>[vmax_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmax_u8*)<br/>[vmaxq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_f32*)<br/>[vmaxq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_f64*)<br/>[vmaxq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_s16*)<br/>[vmaxq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_s32*)<br/>[vmaxq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_s8*)<br/>[vmaxq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_u16*)<br/>[vmaxq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_u32*)<br/>[vmaxq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxq_u8*)<br/></details>|
|vmaxnm|Floating-point maximum number|<details><summary>Click here to expand the API list</summary>[vmaxnm_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxnm_f32*)<br/>[vmaxnm_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxnm_f64*)<br/>[vmaxnmq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxnmq_f32*)<br/>[vmaxnmq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxnmq_f64*)<br/>[vmaxnmv_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxnmv_f32*)<br/>[vmaxnmvq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxnmvq_f32*)<br/>[vmaxnmvq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxnmvq_f64*)<br/></details>|
|vmaxv|Maximum across vector|<details><summary>Click here to expand the API list</summary>[vmaxv_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxv_f32*)<br/>[vmaxv_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxv_s16*)<br/>[vmaxv_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxv_s32*)<br/>[vmaxv_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxv_s8*)<br/>[vmaxv_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxv_u16*)<br/>[vmaxv_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxv_u32*)<br/>[vmaxv_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxv_u8*)<br/>[vmaxvq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_f32*)<br/>[vmaxvq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_f64*)<br/>[vmaxvq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_s16*)<br/>[vmaxvq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_s32*)<br/>[vmaxvq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_s8*)<br/>[vmaxvq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_u16*)<br/>[vmaxvq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_u32*)<br/>[vmaxvq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmaxvq_u8*)<br/></details>|
|vmin|Minimum|<details><summary>Click here to expand the API list</summary>[vmin_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_f32*)<br/>[vmin_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_f64*)<br/>[vmin_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_s16*)<br/>[vmin_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_s32*)<br/>[vmin_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_s8*)<br/>[vmin_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_u16*)<br/>[vmin_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_u32*)<br/>[vmin_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmin_u8*)<br/>[vminq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_f32*)<br/>[vminq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_f64*)<br/>[vminq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_s16*)<br/>[vminq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_s32*)<br/>[vminq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_s8*)<br/>[vminq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_u16*)<br/>[vminq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_u32*)<br/>[vminq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vminq_u8*)<br/></details>|
|vminnm|Floating-point minimum number|<details><summary>Click here to expand the API list</summary>[vminnm_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminnm_f32*)<br/>[vminnm_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vminnm_f64*)<br/>[vminnmq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminnmq_f32*)<br/>[vminnmq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vminnmq_f64*)<br/>[vminnmv_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminnmv_f32*)<br/>[vminnmvq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminnmvq_f32*)<br/>[vminnmvq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vminnmvq_f64*)<br/></details>|
|vminv|Minimum across vector|<details><summary>Click here to expand the API list</summary>[vminv_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminv_f32*)<br/>[vminv_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vminv_s16*)<br/>[vminv_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminv_s32*)<br/>[vminv_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vminv_s8*)<br/>[vminv_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vminv_u16*)<br/>[vminv_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminv_u32*)<br/>[vminv_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vminv_u8*)<br/>[vminvq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_f32*)<br/>[vminvq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_f64*)<br/>[vminvq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_s16*)<br/>[vminvq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_s32*)<br/>[vminvq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_s8*)<br/>[vminvq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_u16*)<br/>[vminvq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_u32*)<br/>[vminvq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vminvq_u8*)<br/></details>|
|vabs|Absolute value|<details><summary>Click here to expand the API list</summary>[vabs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabs_f32*)<br/>[vabs_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabs_f64*)<br/>[vabs_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabs_s16*)<br/>[vabs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabs_s32*)<br/>[vabs_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabs_s64*)<br/>[vabs_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabs_s8*)<br/>[vabsq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabsq_f32*)<br/>[vabsq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabsq_f64*)<br/>[vabsq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vabsq_s16*)<br/>[vabsq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vabsq_s32*)<br/>[vabsq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabsq_s64*)<br/>[vabsq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vabsq_s8*)<br/>[vabsd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vabsd_s64*)<br/></details>|
|vqabs|Saturating absolute value|<details><summary>Click here to expand the API list</summary>[vqabs_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabs_s16*)<br/>[vqabs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabs_s32*)<br/>[vqabs_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabs_s64*)<br/>[vqabs_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabs_s8*)<br/>[vqabsq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabsq_s16*)<br/>[vqabsq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabsq_s32*)<br/>[vqabsq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabsq_s64*)<br/>[vqabsq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabsq_s8*)<br/>[vqabsb_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabsb_s8*)<br/>[vqabsh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabsh_s16*)<br/>[vqabss_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabss_s32*)<br/>[vqabsd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqabsd_s64*)<br/></details>|
|vneg|Negate|<details><summary>Click here to expand the API list</summary>[vneg_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vneg_f32*)<br/>[vneg_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vneg_f64*)<br/>[vneg_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vneg_s16*)<br/>[vneg_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vneg_s32*)<br/>[vneg_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vneg_s64*)<br/>[vneg_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vneg_s8*)<br/>[vnegd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vnegd_s64*)<br/>[vnegq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vnegq_f32*)<br/>[vnegq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vnegq_f64*)<br/>[vnegq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vnegq_s16*)<br/>[vnegq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vnegq_s32*)<br/>[vnegq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vnegq_s64*)<br/>[vnegq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vnegq_s8*)<br/></details>|
|vqneg|Saturating negate|<details><summary>Click here to expand the API list</summary>[vqneg_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqneg_s16*)<br/>[vqneg_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqneg_s32*)<br/>[vqneg_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqneg_s64*)<br/>[vqneg_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqneg_s8*)<br/>[vqnegq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegq_s16*)<br/>[vqnegq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegq_s32*)<br/>[vqnegq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegq_s64*)<br/>[vqnegq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegq_s8*)<br/>[vqnegb_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegb_s8*)<br/>[vqnegh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegh_s16*)<br/>[vqnegs_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegs_s32*)<br/>[vqnegd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqnegd_s64*)<br/></details>|
|vcls|Count leading sign bits|<details><summary>Click here to expand the API list</summary>[vcls_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcls_s16*)<br/>[vcls_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcls_s32*)<br/>[vcls_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcls_s8*)<br/>[vclsq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclsq_s16*)<br/>[vclsq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclsq_s32*)<br/>[vclsq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclsq_s8*)<br/></details>|
|vclz|Count leading zero bits|<details><summary>Click here to expand the API list</summary>[vclz_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclz_s16*)<br/>[vclz_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclz_s32*)<br/>[vclz_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclz_s8*)<br/>[vclz_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclz_u16*)<br/>[vclz_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclz_u32*)<br/>[vclz_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclz_u8*)<br/>[vclzq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclzq_s16*)<br/>[vclzq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclzq_s32*)<br/>[vclzq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclzq_s8*)<br/>[vclzq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclzq_u16*)<br/>[vclzq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclzq_u32*)<br/>[vclzq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclzq_u8*)<br/></details>|
|vcnt|Population count per byte|<details><summary>Click here to expand the API list</summary>[vcnt_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcnt_s8*)<br/>[vcnt_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcnt_u8*)<br/>[vcntq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcntq_s8*)<br/>[vcntq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcntq_u8*)<br/></details>|
|vrecpe|Reciprocal estimate|<details><summary>Click here to expand the API list</summary>[vrecpe_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpe_f32*)<br/> [vrecpe_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpe_f64*)<br/> [vrecpe_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpe_u32*)<br/> [vrecpeq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpeq_f32*)<br/> [vrecpeq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpeq_f64*)<br/> [vrecpeq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpeq_u32*)<br/> [vrecpes_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpes_f32*)<br/> [vrecped_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecped_f64*)<br/></details>|
|vrecps|Reciprocal step|<details><summary>Click here to expand the API list</summary>[vrecps_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecps_f32*)<br/> [vrecps_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecps_f64*)<br/> [vrecpsq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpsq_f32*)<br/> [vrecpsq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpsq_f64*)<br/> [vrecpss_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpss_f32*)<br/> [vrecpsd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpsd_f64*)<br/></details>|
|vrecpx|Floating-point reciprocal exponent|<details><summary>Click here to expand the API list</summary>[vrecpxd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpxd_f64*)<br/>[vrecpxs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrecpxs_f32*)<br/></details>|
|vrsqrte|Reciprocal square root estimate|<details><summary>Click here to expand the API list</summary>[vrsqrte_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrte_f32*)<br/> [vrsqrte_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrte_f64*)<br/> [vrsqrte_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrte_u32*)<br/> [vrsqrteq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrteq_f32*)<br/> [vrsqrteq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrteq_f64*)<br/> [vrsqrteq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrteq_u32*)<br/> [vrsqrtes_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrtes_f32*)<br/> [vrsqrted_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrted_f64*)<br/></details>|
|vrsqrts|Reciprocal square root step|<details><summary>Click here to expand the API list</summary>[vrsqrts_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrts_f32*)<br/> [vrsqrts_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrts_f64*)<br/> [vrsqrtsq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrtsq_f32*)<br/> [vrsqrtsq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrtsq_f64*)<br/> [vrsqrtss_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrtss_f32*)<br/> [vrsqrtsd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsqrtsd_f64*)<br/></details>|
|vmovn|Extract narrow|<details><summary>Click here to expand the API list</summary>[vmovn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_s16*)<br/>[vmovn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_s32*)<br/>[vmovn_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_s64*)<br/>[vmovn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_u16*)<br/>[vmovn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_u32*)<br/>[vmovn_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_u64*)<br/>[vmovn_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_high_s16*)<br/>[vmovn_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_high_s32*)<br/>[vmovn_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_high_s64*)<br/>[vmovn_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_high_u16*)<br/>[vmovn_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_high_u32*)<br/>[vmovn_high_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovn_high_u64*)<br/></details>|
|vmovl|Extract long|<details><summary>Click here to expand the API list</summary>[vmovl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_s16*)<br/>[vmovl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_s32*)<br/>[vmovl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_s8*)<br/>[vmovl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_u16*)<br/>[vmovl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_u32*)<br/>[vmovl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_u8*)<br/>[vmovl_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_high_s16*)<br/>[vmovl_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_high_s32*)<br/>[vmovl_high_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_high_s8*)<br/>[vmovl_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_high_u16*)<br/>[vmovl_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_high_u32*)<br/>[vmovl_high_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmovl_high_u8*)<br/></details>|
|vqmovn|Saturating extract narrow|<details><summary>Click here to expand the API list</summary>[vqmovn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_s16*)<br/>[vqmovn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_s32*)<br/>[vqmovn_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_s64*)<br/>[vqmovn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_u16*)<br/>[vqmovn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_u32*)<br/>[vqmovn_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_u64*)<br/>[vqmovn_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_high_s16*)<br/>[vqmovn_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_high_s32*)<br/>[vqmovn_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_high_s64*)<br/>[vqmovn_high_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_high_u16*)<br/>[vqmovn_high_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_high_u32*)<br/>[vqmovn_high_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovn_high_u64*)<br/>[vqmovnh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovnh_s16*)<br/>[vqmovnh_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovnh_u16*)<br/>[vqmovns_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovns_s32*)<br/>[vqmovns_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovns_u32*)<br/>[vqmovnd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovnd_s64*)<br/>[vqmovnd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovnd_u64*)<br/></details>|
|vqmovun|Signed saturating extract unsigned narrow|<details><summary>Click here to expand the API list</summary>[vqmovun_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovun_s16*)<br/>[vqmovun_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovun_s32*)<br/>[vqmovun_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovun_s64*)<br/>[vqmovun_high_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovun_high_s16*)<br/>[vqmovun_high_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovun_high_s32*)<br/>[vqmovun_high_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovun_high_s64*)<br/>[vqmovunh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovunh_s16*)<br/>[vqmovuns_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovuns_s32*)<br/>[vqmovund_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqmovund_s64*)<br/></details>|

### Comparison

|Operation|Description|APIs|
|---|---|---|
|vceq|Compare bitwise equal|<details><summary>Click here to expand the API list</summary>[vceq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_f32*)<br/>[vceq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_f64*)<br/>[vceq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_s16*)<br/>[vceq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_s32*)<br/>[vceq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_s64*)<br/>[vceq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_s8*)<br/>[vceq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_u16*)<br/>[vceq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_u32*)<br/>[vceq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_u64*)<br/>[vceq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceq_u8*)<br/>[vceqq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_f32*)<br/>[vceqq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_f64*)<br/>[vceqq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_s16*)<br/>[vceqq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_s32*)<br/>[vceqq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_s64*)<br/>[vceqq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_s8*)<br/>[vceqq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_u16*)<br/>[vceqq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_u32*)<br/>[vceqq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_u64*)<br/>[vceqq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqq_u8*)<br/>[vceqs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqs_f32*)<br/>[vceqd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqd_f64*)<br/>[vceqd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqd_s64*)<br/>[vceqd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqd_u64*)<br/></details>|
|vceqz|Compare bitwise equal to zero|<details><summary>Click here to expand the API list</summary>[vceqz_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_f32*)<br/>[vceqz_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_f64*)<br/>[vceqz_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_s16*)<br/>[vceqz_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_s32*)<br/>[vceqz_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_s64*)<br/>[vceqz_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_s8*)<br/>[vceqz_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_u16*)<br/>[vceqz_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_u32*)<br/>[vceqz_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_u64*)<br/>[vceqz_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqz_u8*)<br/>[vceqzq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_f32*)<br/>[vceqzq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_f64*)<br/>[vceqzq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_s16*)<br/>[vceqzq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_s32*)<br/>[vceqzq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_s64*)<br/>[vceqzq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_s8*)<br/>[vceqzq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_u16*)<br/>[vceqzq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_u32*)<br/>[vceqzq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_u64*)<br/>[vceqzq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzq_u8*)<br/>[vceqzs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzs_f32*)<br/>[vceqzd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzd_f64*)<br/>[vceqzd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzd_s64*)<br/>[vceqzd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vceqzd_u64*)<br/></details>|
|vcge|Compare greater than or equal|<details><summary>Click here to expand the API list</summary>[vcge_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_f32*)<br/>[vcge_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_f64*)<br/>[vcge_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_s16*)<br/>[vcge_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_s32*)<br/>[vcge_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_s64*)<br/>[vcge_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_s8*)<br/>[vcge_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_u16*)<br/>[vcge_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_u32*)<br/>[vcge_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_u64*)<br/>[vcge_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcge_u8*)<br/>[vcgeq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_f32*)<br/>[vcgeq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_f64*)<br/>[vcgeq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_s16*)<br/>[vcgeq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_s32*)<br/>[vcgeq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_s64*)<br/>[vcgeq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_s8*)<br/>[vcgeq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_u16*)<br/>[vcgeq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_u32*)<br/>[vcgeq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_u64*)<br/>[vcgeq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgeq_u8*)<br/>[vcges_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcges_f32*)<br/>[vcged_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcged_f64*)<br/>[vcged_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcged_s64*)<br/>[vcged_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcged_u64*)<br/></details>|
|vcgez|Compare greater than or equal to zero|<details><summary>Click here to expand the API list</summary>[vcgez_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgez_f32*)<br/>[vcgez_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgez_f64*)<br/>[vcgez_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgez_s16*)<br/>[vcgez_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgez_s32*)<br/>[vcgez_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgez_s64*)<br/>[vcgez_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgez_s8*)<br/>[vcgezq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezq_f32*)<br/>[vcgezq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezq_f64*)<br/>[vcgezq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezq_s16*)<br/>[vcgezq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezq_s32*)<br/>[vcgezq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezq_s64*)<br/>[vcgezq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezq_s8*)<br/>[vcgezs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezs_f32*)<br/>[vcgezd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezd_f64*)<br/>[vcgezd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgezd_s64*)<br/></details>|
|vcle|Compare less than or equal|<details><summary>Click here to expand the API list</summary>[vcle_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_f32*)<br/>[vcle_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_f64*)<br/>[vcle_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_s16*)<br/>[vcle_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_s32*)<br/>[vcle_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_s64*)<br/>[vcle_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_s8*)<br/>[vcle_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_u16*)<br/>[vcle_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_u32*)<br/>[vcle_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_u64*)<br/>[vcle_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcle_u8*)<br/>[vcleq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_f32*)<br/>[vcleq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_f64*)<br/>[vcleq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_s16*)<br/>[vcleq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_s32*)<br/>[vcleq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_s64*)<br/>[vcleq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_s8*)<br/>[vcleq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_u16*)<br/>[vcleq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_u32*)<br/>[vcleq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_u64*)<br/>[vcleq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcleq_u8*)<br/>[vcles_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcles_f32*)<br/>[vcled_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcled_f64*)<br/>[vcled_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcled_s64*)<br/>[vcled_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcled_u64*)<br/></details>|
|vclez|Compare less than or equal to zero|<details><summary>Click here to expand the API list</summary>[vclez_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclez_f32*)<br/>[vclez_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclez_f64*)<br/>[vclez_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclez_s16*)<br/>[vclez_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclez_s32*)<br/>[vclez_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclez_s64*)<br/>[vclez_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclez_s8*)<br/>[vclezq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezq_f32*)<br/>[vclezq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezq_f64*)<br/>[vclezq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezq_s16*)<br/>[vclezq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezq_s32*)<br/>[vclezq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezq_s64*)<br/>[vclezq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezq_s8*)<br/>[vclezs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezs_f32*)<br/>[vclezd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezd_f64*)<br/>[vclezd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclezd_s64*)<br/></details>|
|vcgt|Compare greater than|<details><summary>Click here to expand the API list</summary>[vcgt_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_f32*)<br/>[vcgt_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_f64*)<br/>[vcgt_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_s16*)<br/>[vcgt_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_s32*)<br/>[vcgt_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_s64*)<br/>[vcgt_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_s8*)<br/>[vcgt_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_u16*)<br/>[vcgt_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_u32*)<br/>[vcgt_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_u64*)<br/>[vcgt_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgt_u8*)<br/>[vcgtq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_f32*)<br/>[vcgtq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_f64*)<br/>[vcgtq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_s16*)<br/>[vcgtq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_s32*)<br/>[vcgtq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_s64*)<br/>[vcgtq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_s8*)<br/>[vcgtq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_u16*)<br/>[vcgtq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_u32*)<br/>[vcgtq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_u64*)<br/>[vcgtq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtq_u8*)<br/>[vcgts_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgts_f32*)<br/>[vcgtd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtd_f64*)<br/>[vcgtd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtd_s64*)<br/>[vcgtd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtd_u64*)<br/></details>|
|vcgtz|Compare greater than zero|<details><summary>Click here to expand the API list</summary>[vcgtz_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtz_f32*)<br/>[vcgtz_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtz_f64*)<br/>[vcgtz_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtz_s16*)<br/>[vcgtz_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtz_s32*)<br/>[vcgtz_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtz_s64*)<br/>[vcgtz_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtz_s8*)<br/>[vcgtzq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzq_f32*)<br/>[vcgtzq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzq_f64*)<br/>[vcgtzq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzq_s16*)<br/>[vcgtzq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzq_s32*)<br/>[vcgtzq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzq_s64*)<br/>[vcgtzq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzq_s8*)<br/>[vcgtzs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzs_f32*)<br/>[vcgtzd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzd_f64*)<br/>[vcgtzd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcgtzd_s64*)<br/></details>|
|vclt|Compare less than|<details><summary>Click here to expand the API list</summary>[vclt_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_f32*)<br/>[vclt_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_f64*)<br/>[vclt_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_s16*)<br/>[vclt_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_s32*)<br/>[vclt_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_s64*)<br/>[vclt_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_s8*)<br/>[vclt_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_u16*)<br/>[vclt_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_u32*)<br/>[vclt_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_u64*)<br/>[vclt_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vclt_u8*)<br/>[vcltq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_f32*)<br/>[vcltq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_f64*)<br/>[vcltq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_s16*)<br/>[vcltq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_s32*)<br/>[vcltq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_s64*)<br/>[vcltq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_s8*)<br/>[vcltq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_u16*)<br/>[vcltq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_u32*)<br/>[vcltq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_u64*)<br/>[vcltq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltq_u8*)<br/>[vclts_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vclts_f32*)<br/>[vcltd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltd_f64*)<br/>[vcltd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltd_s64*)<br/>[vcltd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltd_u64*)<br/></details>|
|vcltz|Compare less than zero|<details><summary>Click here to expand the API list</summary>[vcltz_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltz_f32*)<br/>[vcltz_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltz_f64*)<br/>[vcltz_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltz_s16*)<br/>[vcltz_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltz_s32*)<br/>[vcltz_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltz_s64*)<br/>[vcltz_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltz_s8*)<br/>[vcltzq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzq_f32*)<br/>[vcltzq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzq_f64*)<br/>[vcltzq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzq_s16*)<br/>[vcltzq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzq_s32*)<br/>[vcltzq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzq_s64*)<br/>[vcltzq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzq_s8*)<br/>[vcltzs_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzs_f32*)<br/>[vcltzd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzd_f64*)<br/>[vcltzd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcltzd_s64*)<br/></details>|
|vcage|Floating-point absolute compare greater than or equal|<details><summary>Click here to expand the API list</summary>[vcage_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcage_f32*)<br/>[vcage_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcage_f64*)<br/>[vcageq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcageq_f32*)<br/>[vcageq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcageq_f64*)<br/>[vcages_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcages_f32*)<br/>[vcaged_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcaged_f64*)<br/></details>|
|vcagt|Floating-point absolute compare greater than|<details><summary>Click here to expand the API list</summary>[vcagt_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcagt_f32*)<br/>[vcagt_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcagt_f64*)<br/>[vcagtq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcagtq_f32*)<br/>[vcagtq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcagtq_f64*)<br/>[vcagts_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcagts_f32*)<br/>[vcagtd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcagtd_f64*)<br/></details>|
|vcale|Floating-point absolute compare less than or equal|<details><summary>Click here to expand the API list</summary>[vcale_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcale_f32*)<br/>[vcale_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcale_f64*)<br/>[vcaleq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcaleq_f32*)<br/>[vcaleq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcaleq_f64*)<br/>[vcales_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcales_f32*)<br/>[vcaled_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcaled_f64*)<br/></details>|
|vcalt|Floating-point absolute compare less than|<details><summary>Click here to expand the API list</summary>[vcalt_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcalt_f32*)<br/>[vcalt_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcalt_f64*)<br/>[vcaltq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcaltq_f32*)<br/>[vcaltq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcaltq_f64*)<br/>[vcalts_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcalts_f32*)<br/>[vcaltd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcaltd_f64*)<br/></details>|

### Bitwise

|Operation|Description|APIs|
|---|---|---|
|vtst|Test bits nonzero|<details><summary>Click here to expand the API list</summary>[vtst_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_s16*)<br/>[vtst_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_s32*)<br/>[vtst_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_s64*)<br/>[vtst_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_s8*)<br/>[vtst_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_u16*)<br/>[vtst_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_u32*)<br/>[vtst_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_u64*)<br/>[vtst_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtst_u8*)<br/>[vtstd_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstd_s64*)<br/>[vtstd_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstd_u64*)<br/>[vtstq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_s16*)<br/>[vtstq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_s32*)<br/>[vtstq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_s64*)<br/>[vtstq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_s8*)<br/>[vtstq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_u16*)<br/>[vtstq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_u32*)<br/>[vtstq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_u64*)<br/>[vtstq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtstq_u8*)<br/></details>|
|vmvn|Bitwise NOT|<details><summary>Click here to expand the API list</summary>[vmvn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvn_s16*)<br/>[vmvn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvn_s32*)<br/>[vmvn_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvn_s8*)<br/>[vmvn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvn_u16*)<br/>[vmvn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvn_u32*)<br/>[vmvn_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvn_u8*)<br/>[vmvnq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvnq_s16*)<br/>[vmvnq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvnq_s32*)<br/>[vmvnq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvnq_s8*)<br/>[vmvnq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvnq_u16*)<br/>[vmvnq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvnq_u32*)<br/>[vmvnq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vmvnq_u8*)<br/></details>|
|vand|Bitwise AND|<details><summary>Click here to expand the API list</summary>[vand_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_s16*)<br/>[vand_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_s32*)<br/>[vand_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_s64*)<br/>[vand_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_s8*)<br/>[vand_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_u16*)<br/>[vand_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_u32*)<br/>[vand_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_u64*)<br/>[vand_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vand_u8*)<br/>[vandq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_s16*)<br/>[vandq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_s32*)<br/>[vandq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_s64*)<br/>[vandq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_s8*)<br/>[vandq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_u16*)<br/>[vandq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_u32*)<br/>[vandq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_u64*)<br/>[vandq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vandq_u8*)<br/></details>|
|vorr|Bitwise OR|<details><summary>Click here to expand the API list</summary>[vorr_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_s16*)<br/>[vorr_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_s32*)<br/>[vorr_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_s64*)<br/>[vorr_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_s8*)<br/>[vorr_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_u16*)<br/>[vorr_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_u32*)<br/>[vorr_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_u64*)<br/>[vorr_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vorr_u8*)<br/>[vorrq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_s16*)<br/>[vorrq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_s32*)<br/>[vorrq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_s64*)<br/>[vorrq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_s8*)<br/>[vorrq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_u16*)<br/>[vorrq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_u32*)<br/>[vorrq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_u64*)<br/>[vorrq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vorrq_u8*)<br/></details>|
|vorn|Bitwise OR NOT|<details><summary>Click here to expand the API list</summary>[vorn_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_s16*)<br/>[vorn_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_s32*)<br/>[vorn_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_s64*)<br/>[vorn_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_s8*)<br/>[vorn_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_u16*)<br/>[vorn_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_u32*)<br/>[vorn_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_u64*)<br/>[vorn_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vorn_u8*)<br/>[vornq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_s16*)<br/>[vornq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_s32*)<br/>[vornq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_s64*)<br/>[vornq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_s8*)<br/>[vornq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_u16*)<br/>[vornq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_u32*)<br/>[vornq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_u64*)<br/>[vornq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vornq_u8*)<br/></details>|
|veor|Bitwise exclusive OR|<details><summary>Click here to expand the API list</summary>[veor_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_s16*)<br/>[veor_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_s32*)<br/>[veor_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_s64*)<br/>[veor_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_s8*)<br/>[veor_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_u16*)<br/>[veor_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_u32*)<br/>[veor_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_u64*)<br/>[veor_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.veor_u8*)<br/>[veorq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_s16*)<br/>[veorq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_s32*)<br/>[veorq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_s64*)<br/>[veorq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_s8*)<br/>[veorq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_u16*)<br/>[veorq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_u32*)<br/>[veorq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_u64*)<br/>[veorq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.veorq_u8*)<br/></details>|
|vbic|Bitwise bit clear|<details><summary>Click here to expand the API list</summary>[vbic_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_s16*)<br/>[vbic_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_s32*)<br/>[vbic_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_s64*)<br/>[vbic_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_s8*)<br/>[vbic_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_u16*)<br/>[vbic_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_u32*)<br/>[vbic_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_u64*)<br/>[vbic_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbic_u8*)<br/>[vbicq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_s16*)<br/>[vbicq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_s32*)<br/>[vbicq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_s64*)<br/>[vbicq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_s8*)<br/>[vbicq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_u16*)<br/>[vbicq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_u32*)<br/>[vbicq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_u64*)<br/>[vbicq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbicq_u8*)<br/></details>|
|vbsl|Bitwise select|<details><summary>Click here to expand the API list</summary>[vbsl_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_f32*)<br/>[vbsl_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_f64*)<br/>[vbsl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_s16*)<br/>[vbsl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_s32*)<br/>[vbsl_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_s64*)<br/>[vbsl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_s8*)<br/>[vbsl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_u16*)<br/>[vbsl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_u32*)<br/>[vbsl_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_u64*)<br/>[vbsl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbsl_u8*)<br/>[vbslq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_f32*)<br/>[vbslq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_f64*)<br/>[vbslq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_s16*)<br/>[vbslq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_s32*)<br/>[vbslq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_s64*)<br/>[vbslq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_s8*)<br/>[vbslq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_u16*)<br/>[vbslq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_u32*)<br/>[vbslq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_u64*)<br/>[vbslq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vbslq_u8*)<br/></details>|

### Shift

|Operation|Description|APIs|
|---|---|---|
|vshl|Shift left (register)|<details><summary>Click here to expand the API list</summary>[vshl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_s16*)<br/>[vshl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_s32*)<br/>[vshl_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_s64*)<br/>[vshl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_s8*)<br/>[vshl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_u16*)<br/>[vshl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_u32*)<br/>[vshl_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_u64*)<br/>[vshl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_u8*)<br/>[vshlq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_s16*)<br/>[vshlq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_s32*)<br/>[vshlq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_s64*)<br/>[vshlq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_s8*)<br/>[vshlq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_u16*)<br/>[vshlq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_u32*)<br/>[vshlq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_u64*)<br/>[vshlq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_u8*)<br/>[vshld_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshld_s64*)<br/>[vshld_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshld_u64*)<br/></details>|
|vqshl|Saturating shift left (register)|<details><summary>Click here to expand the API list</summary>[vqshl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_s16*)<br/>[vqshl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_s32*)<br/>[vqshl_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_s64*)<br/>[vqshl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_s8*)<br/>[vqshl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_u16*)<br/>[vqshl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_u32*)<br/>[vqshl_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_u64*)<br/>[vqshl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_u8*)<br/>[vqshlq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_s16*)<br/>[vqshlq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_s32*)<br/>[vqshlq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_s64*)<br/>[vqshlq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_s8*)<br/>[vqshlq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_u16*)<br/>[vqshlq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_u32*)<br/>[vqshlq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_u64*)<br/>[vqshlq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_u8*)<br/>[vqshlb_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlb_s8*)<br/>[vqshlb_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlb_u8*)<br/>[vqshlh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlh_s16*)<br/>[vqshlh_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlh_u16*)<br/>[vqshls_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshls_s32*)<br/>[vqshls_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshls_u32*)<br/>[vqshld_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshld_s64*)<br/>[vqshld_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshld_u64*)<br/></details>|
|vqshl_n|Saturating shift left (immediate)|<details><summary>Click here to expand the API list</summary>[vqshl_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_s16*)<br/>[vqshl_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_s32*)<br/>[vqshl_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_s64*)<br/>[vqshl_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_s8*)<br/>[vqshl_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_u16*)<br/>[vqshl_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_u32*)<br/>[vqshl_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_u64*)<br/>[vqshl_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshl_n_u8*)<br/>[vqshlq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_s16*)<br/>[vqshlq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_s32*)<br/>[vqshlq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_s64*)<br/>[vqshlq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_s8*)<br/>[vqshlq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_u16*)<br/>[vqshlq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_u32*)<br/>[vqshlq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_u64*)<br/>[vqshlq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlq_n_u8*)<br/>[vqshlb_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlb_n_s8*)<br/>[vqshlb_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlb_n_u8*)<br/>[vqshlh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlh_n_s16*)<br/>[vqshlh_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlh_n_u16*)<br/>[vqshls_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshls_n_s32*)<br/>[vqshls_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshls_n_u32*)<br/>[vqshld_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshld_n_s64*)<br/>[vqshld_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshld_n_u64*)<br/></details>|
|vqshlu_n|Saturating shift left unsigned (immediate)|<details><summary>Click here to expand the API list</summary>[vqshlu_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlu_n_s16*)<br/>[vqshlu_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlu_n_s32*)<br/>[vqshlu_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlu_n_s64*)<br/>[vqshlu_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlu_n_s8*)<br/>[vqshlub_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlub_n_s8*)<br/>[vqshlud_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlud_n_s64*)<br/>[vqshluh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshluh_n_s16*)<br/>[vqshluq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshluq_n_s16*)<br/>[vqshluq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshluq_n_s32*)<br/>[vqshluq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshluq_n_s64*)<br/>[vqshluq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshluq_n_s8*)<br/>[vqshlus_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshlus_n_s32*)<br/></details>|
|vrshl|Rounding shift left (register)|<details><summary>Click here to expand the API list</summary>[vrshl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_s16*)<br/>[vrshl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_s32*)<br/>[vrshl_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_s64*)<br/>[vrshl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_s8*)<br/>[vrshl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_u16*)<br/>[vrshl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_u32*)<br/>[vrshl_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_u64*)<br/>[vrshl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshl_u8*)<br/>[vrshlq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_s16*)<br/>[vrshlq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_s32*)<br/>[vrshlq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_s64*)<br/>[vrshlq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_s8*)<br/>[vrshlq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_u16*)<br/>[vrshlq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_u32*)<br/>[vrshlq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_u64*)<br/>[vrshlq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshlq_u8*)<br/>[vrshld_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshld_s64*)<br/>[vrshld_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshld_u64*)<br/></details>|
|vqrshl|Saturating rounding shift left (register)|<details><summary>Click here to expand the API list</summary>[vqrshl_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_s16*)<br/>[vqrshl_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_s32*)<br/>[vqrshl_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_s64*)<br/>[vqrshl_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_s8*)<br/>[vqrshl_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_u16*)<br/>[vqrshl_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_u32*)<br/>[vqrshl_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_u64*)<br/>[vqrshl_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshl_u8*)<br/>[vqrshlq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_s16*)<br/>[vqrshlq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_s32*)<br/>[vqrshlq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_s64*)<br/>[vqrshlq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_s8*)<br/>[vqrshlq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_u16*)<br/>[vqrshlq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_u32*)<br/>[vqrshlq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_u64*)<br/>[vqrshlq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlq_u8*)<br/>[vqrshlb_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlb_s8*)<br/>[vqrshlb_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlb_u8*)<br/>[vqrshlh_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlh_s16*)<br/>[vqrshlh_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshlh_u16*)<br/>[vqrshls_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshls_s32*)<br/>[vqrshls_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshls_u32*)<br/>[vqrshld_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshld_s64*)<br/>[vqrshld_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshld_u64*)<br/></details>|
|vshl_n|Shift left (immediate)|<details><summary>Click here to expand the API list</summary>[vshl_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_s16*)<br/>[vshl_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_s32*)<br/>[vshl_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_s64*)<br/>[vshl_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_s8*)<br/>[vshl_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_u16*)<br/>[vshl_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_u32*)<br/>[vshl_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_u64*)<br/>[vshl_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshl_n_u8*)<br/>[vshlq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_s16*)<br/>[vshlq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_s32*)<br/>[vshlq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_s64*)<br/>[vshlq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_s8*)<br/>[vshlq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_u16*)<br/>[vshlq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_u32*)<br/>[vshlq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_u64*)<br/>[vshlq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshlq_n_u8*)<br/>[vshld_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshld_n_s64*)<br/>[vshld_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshld_n_u64*)<br/></details>|
|vshll_n|Shift left long (immediate)|<details><summary>Click here to expand the API list</summary>[vshll_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_n_s16*)<br/>[vshll_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_n_s32*)<br/>[vshll_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_n_s8*)<br/>[vshll_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_n_u16*)<br/>[vshll_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_n_u32*)<br/>[vshll_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_n_u8*)<br/>[vshll_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_high_n_s16*)<br/>[vshll_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_high_n_s32*)<br/>[vshll_high_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_high_n_s8*)<br/>[vshll_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_high_n_u16*)<br/>[vshll_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_high_n_u32*)<br/>[vshll_high_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshll_high_n_u8*)<br/></details>|
|vshr_n|Shift right (immediate)|<details><summary>Click here to expand the API list</summary>[vshr_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_s16*)<br/>[vshr_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_s32*)<br/>[vshr_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_s64*)<br/>[vshr_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_s8*)<br/>[vshr_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_u16*)<br/>[vshr_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_u32*)<br/>[vshr_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_u64*)<br/>[vshr_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshr_n_u8*)<br/>[vshrq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_s16*)<br/>[vshrq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_s32*)<br/>[vshrq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_s64*)<br/>[vshrq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_s8*)<br/>[vshrq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_u16*)<br/>[vshrq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_u32*)<br/>[vshrq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_u64*)<br/>[vshrq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrq_n_u8*)<br/>[vshrd_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrd_n_s64*)<br/>[vshrd_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrd_n_u64*)<br/></details>|
|vrshr_n|Rounding right left (register)|<details><summary>Click here to expand the API list</summary>[vrshr_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_s16*)<br/>[vrshr_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_s32*)<br/>[vrshr_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_s64*)<br/>[vrshr_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_s8*)<br/>[vrshr_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_u16*)<br/>[vrshr_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_u32*)<br/>[vrshr_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_u64*)<br/>[vrshr_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshr_n_u8*)<br/>[vrshrq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_s16*)<br/>[vrshrq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_s32*)<br/>[vrshrq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_s64*)<br/>[vrshrq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_s8*)<br/>[vrshrq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_u16*)<br/>[vrshrq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_u32*)<br/>[vrshrq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_u64*)<br/>[vrshrq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrq_n_u8*)<br/>[vrshrd_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrd_n_s64*)<br/>[vrshrd_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrd_n_u64*)<br/></details>|
|vshrn_n|Shift right narrow (immediate)|<details><summary>Click here to expand the API list</summary>[vshrn_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_n_s16*)<br/>[vshrn_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_n_s32*)<br/>[vshrn_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_n_s64*)<br/>[vshrn_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_n_u16*)<br/>[vshrn_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_n_u32*)<br/>[vshrn_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_n_u64*)<br/>[vshrn_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_high_n_s16*)<br/>[vshrn_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_high_n_s32*)<br/>[vshrn_high_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_high_n_s64*)<br/>[vshrn_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_high_n_u16*)<br/>[vshrn_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_high_n_u32*)<br/>[vshrn_high_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vshrn_high_n_u64*)<br/></details>|
|vqshrun_n|Signed saturating shift right<br/> unsigned narrow (immediate)|<details><summary>Click here to expand the API list</summary>[vqshrun_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrun_n_s16*)<br/> [vqshrun_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrun_n_s32*)<br/> [vqshrun_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrun_n_s64*)<br/> [vqshrunh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrunh_n_s16*)<br/> [vqshruns_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshruns_n_s32*)<br/> [vqshrund_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrund_n_s64*)<br/> [vqshrun_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrun_high_n_s16*)<br/> [vqshrun_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrun_high_n_s32*)<br/> [vqshrun_high_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrun_high_n_s64*)<br/></details>|
|vqrshrun_n|Signed saturating rounded shift right<br/> unsigned narrow (immediate)|<details><summary>Click here to expand the API list</summary>[vqrshrun_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrun_n_s16*)<br/> [vqrshrun_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrun_n_s32*)<br/> [vqrshrun_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrun_n_s64*)<br/> [vqrshrunh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrunh_n_s16*)<br/> [vqrshruns_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshruns_n_s32*)<br/> [vqrshrund_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrund_n_s64*)<br/> [vqrshrun_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrun_high_n_s16*)<br/> [vqrshrun_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrun_high_n_s32*)<br/> [vqrshrun_high_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrun_high_n_s64*)<br/></details>|
|vqshrn_n|Signed saturating shift right narrow (immediate)|<details><summary>Click here to expand the API list</summary>[vqshrn_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_n_s16*)<br/>[vqshrn_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_n_s32*)<br/>[vqshrn_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_n_s64*)<br/>[vqshrn_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_n_u16*)<br/>[vqshrn_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_n_u32*)<br/>[vqshrn_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_n_u64*)<br/>[vqshrnh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrnh_n_s16*)<br/>[vqshrnh_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrnh_n_u16*)<br/>[vqshrns_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrns_n_s32*)<br/>[vqshrns_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrns_n_u32*)<br/>[vqshrnd_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrnd_n_s64*)<br/>[vqshrnd_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrnd_n_u64*)<br/>[vqshrn_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_high_n_s16*)<br/>[vqshrn_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_high_n_s32*)<br/>[vqshrn_high_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_high_n_s64*)<br/>[vqshrn_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_high_n_u16*)<br/>[vqshrn_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_high_n_u32*)<br/>[vqshrn_high_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqshrn_high_n_u64*)<br/></details>|
|vrshrn_n|Rounding shift right narrow (immediate)|<details><summary>Click here to expand the API list</summary>[vrshrn_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_n_s16*)<br/>[vrshrn_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_n_s32*)<br/>[vrshrn_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_n_s64*)<br/>[vrshrn_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_n_u16*)<br/>[vrshrn_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_n_u32*)<br/>[vrshrn_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_n_u64*)<br/>[vrshrn_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_high_n_s16*)<br/>[vrshrn_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_high_n_s32*)<br/>[vrshrn_high_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_high_n_s64*)<br/>[vrshrn_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_high_n_u16*)<br/>[vrshrn_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_high_n_u32*)<br/>[vrshrn_high_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrshrn_high_n_u64*)<br/></details>|
|vqrshrn_n|Signed saturating rounded shift right narrow (immediate)|<details><summary>Click here to expand the API list</summary>[vqrshrn_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_n_s16*)<br/> [vqrshrn_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_n_s32*)<br/> [vqrshrn_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_n_s64*)<br/> [vqrshrn_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_n_u16*)<br/> [vqrshrn_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_n_u32*)<br/> [vqrshrn_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_n_u64*)<br/> [vqrshrnh_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrnh_n_s16*)<br/> [vqrshrnh_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrnh_n_u16*)<br/> [vqrshrns_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrns_n_s32*)<br/> [vqrshrns_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrns_n_u32*)<br/> [vqrshrnd_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrnd_n_s64*)<br/> [vqrshrnd_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrnd_n_u64*)<br/> [vqrshrn_high_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_high_n_s16*)<br/> [vqrshrn_high_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_high_n_s32*)<br/> [vqrshrn_high_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_high_n_s64*)<br/> [vqrshrn_high_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_high_n_u16*)<br/> [vqrshrn_high_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_high_n_u32*)<br/> [vqrshrn_high_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vqrshrn_high_n_u64*)<br/></details>|
|vsra_n|Signed shift right and accumulate (immediate)|<details><summary>Click here to expand the API list</summary>[vsra_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_s16*)<br/>[vsra_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_s32*)<br/>[vsra_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_s64*)<br/>[vsra_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_s8*)<br/>[vsra_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_u16*)<br/>[vsra_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_u32*)<br/>[vsra_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_u64*)<br/>[vsra_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsra_n_u8*)<br/>[vsraq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_s16*)<br/>[vsraq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_s32*)<br/>[vsraq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_s64*)<br/>[vsraq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_s8*)<br/>[vsraq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_u16*)<br/>[vsraq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_u32*)<br/>[vsraq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_u64*)<br/>[vsraq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsraq_n_u8*)<br/>[vsrad_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsrad_n_s64*)<br/>[vsrad_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsrad_n_u64*)<br/></details>|
|vrsra_n|Signed rounding shift right and accumulate (immediate)|<details><summary>Click here to expand the API list</summary>[vrsra_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_s16*)<br/>[vrsra_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_s32*)<br/>[vrsra_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_s64*)<br/>[vrsra_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_s8*)<br/>[vrsra_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_u16*)<br/>[vrsra_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_u32*)<br/>[vrsra_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_u64*)<br/>[vrsra_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsra_n_u8*)<br/>[vrsraq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_s16*)<br/>[vrsraq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_s32*)<br/>[vrsraq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_s64*)<br/>[vrsraq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_s8*)<br/>[vrsraq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_u16*)<br/>[vrsraq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_u32*)<br/>[vrsraq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_u64*)<br/>[vrsraq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsraq_n_u8*)<br/>[vrsrad_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsrad_n_s64*)<br/>[vrsrad_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrsrad_n_u64*)<br/></details>|
|vsri_n|Shift right and insert (immediate)|<details><summary>Click here to expand the API list</summary>[vsri_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_s16*)<br/>[vsri_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_s32*)<br/>[vsri_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_s64*)<br/>[vsri_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_s8*)<br/>[vsri_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_u16*)<br/>[vsri_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_u32*)<br/>[vsri_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_u64*)<br/>[vsri_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsri_n_u8*)<br/>[vsriq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_s16*)<br/>[vsriq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_s32*)<br/>[vsriq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_s64*)<br/>[vsriq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_s8*)<br/>[vsriq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_u16*)<br/>[vsriq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_u32*)<br/>[vsriq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_u64*)<br/>[vsriq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsriq_n_u8*)<br/>[vsrid_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsrid_n_s64*)<br/>[vsrid_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsrid_n_u64*)<br/></details>|
|vsli_n|Shift left and insert (immediate)|<details><summary>Click here to expand the API list</summary>[vsli_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_s16*)<br/>[vsli_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_s32*)<br/>[vsli_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_s64*)<br/>[vsli_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_s8*)<br/>[vsli_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_u16*)<br/>[vsli_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_u32*)<br/>[vsli_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_u64*)<br/>[vsli_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsli_n_u8*)<br/>[vsliq_n_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_s16*)<br/>[vsliq_n_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_s32*)<br/>[vsliq_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_s64*)<br/>[vsliq_n_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_s8*)<br/>[vsliq_n_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_u16*)<br/>[vsliq_n_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_u32*)<br/>[vsliq_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_u64*)<br/>[vsliq_n_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsliq_n_u8*)<br/>[vslid_n_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vslid_n_s64*)<br/>[vslid_n_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vslid_n_u64*)<br/></details>|

### Floating-point

|Operation|Description|APIs|
|---|---|---|
|vcvt|Convert to/from another precision or fixed point,<br/>rounding towards zero|<details><summary>Click here to expand the API list</summary>[vcvt_f32_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_f32_f64*)<br/>[vcvt_f32_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_f32_s32*)<br/>[vcvt_f32_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_f32_u32*)<br/>[vcvt_f64_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_f64_f32*)<br/>[vcvt_f64_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_f64_s64*)<br/>[vcvt_f64_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_f64_u64*)<br/>[vcvt_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_s32_f32*)<br/>[vcvt_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_s64_f64*)<br/>[vcvt_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_u32_f32*)<br/>[vcvt_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_u64_f64*)<br/>[vcvtq_f32_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_f32_s32*)<br/>[vcvtq_f32_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_f32_u32*)<br/>[vcvtq_f64_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_f64_s64*)<br/>[vcvtq_f64_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_f64_u64*)<br/>[vcvtq_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_s32_f32*)<br/>[vcvtq_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_s64_f64*)<br/>[vcvtq_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_u32_f32*)<br/>[vcvtq_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_u64_f64*)<br/>[vcvts_f32_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_f32_s32*)<br/>[vcvts_f32_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_f32_u32*)<br/>[vcvts_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_s32_f32*)<br/>[vcvts_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_u32_f32*)<br/>[vcvtd_f64_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_f64_s64*)<br/>[vcvtd_f64_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_f64_u64*)<br/>[vcvtd_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_s64_f64*)<br/>[vcvtd_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_u64_f64*)<br/>[vcvt_high_f32_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_high_f32_f64*)<br/>[vcvt_high_f64_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_high_f64_f32*)<br/></details>|
|vcvta|Convert to integer, rounding to nearest with ties to away|<details><summary>Click here to expand the API list</summary>[vcvta_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvta_s32_f32*)<br/>[vcvta_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvta_s64_f64*)<br/>[vcvta_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvta_u32_f32*)<br/>[vcvta_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvta_u64_f64*)<br/>[vcvtad_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtad_s64_f64*)<br/>[vcvtad_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtad_u64_f64*)<br/>[vcvtaq_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtaq_s32_f32*)<br/>[vcvtaq_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtaq_s64_f64*)<br/>[vcvtaq_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtaq_u32_f32*)<br/>[vcvtaq_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtaq_u64_f64*)<br/>[vcvtas_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtas_s32_f32*)<br/>[vcvtas_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtas_u32_f32*)<br/></details>|
|vcvtm|Convert to integer, rounding towards minus infinity|<details><summary>Click here to expand the API list</summary>[vcvtm_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtm_s32_f32*)<br/>[vcvtm_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtm_s64_f64*)<br/>[vcvtm_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtm_u32_f32*)<br/>[vcvtm_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtm_u64_f64*)<br/>[vcvtmq_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtmq_s32_f32*)<br/>[vcvtmq_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtmq_s64_f64*)<br/>[vcvtmq_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtmq_u32_f32*)<br/>[vcvtmq_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtmq_u64_f64*)<br/>[vcvtms_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtms_s32_f32*)<br/>[vcvtms_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtms_u32_f32*)<br/>[vcvtmd_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtmd_s64_f64*)<br/>[vcvtmd_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtmd_u64_f64*)<br/></details>|
|vcvtn|Convert to integer, rounding to nearest with ties to even|<details><summary>Click here to expand the API list</summary>[vcvtn_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtn_s32_f32*)<br/>[vcvtn_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtn_s64_f64*)<br/>[vcvtn_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtn_u32_f32*)<br/>[vcvtn_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtn_u64_f64*)<br/>[vcvtnq_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtnq_s32_f32*)<br/>[vcvtnq_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtnq_s64_f64*)<br/>[vcvtnq_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtnq_u32_f32*)<br/>[vcvtnq_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtnq_u64_f64*)<br/>[vcvtns_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtns_s32_f32*)<br/>[vcvtns_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtns_u32_f32*)<br/>[vcvtnd_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtnd_s64_f64*)<br/>[vcvtnd_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtnd_u64_f64*)<br/></details>|
|vcvtp|Convert to integer, rounding towards plus infinity|<details><summary>Click here to expand the API list</summary>[vcvtp_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtp_s32_f32*)<br/>[vcvtp_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtp_s64_f64*)<br/>[vcvtp_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtp_u32_f32*)<br/>[vcvtp_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtp_u64_f64*)<br/>[vcvtpq_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtpq_s32_f32*)<br/>[vcvtpq_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtpq_s64_f64*)<br/>[vcvtpq_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtpq_u32_f32*)<br/>[vcvtpq_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtpq_u64_f64*)<br/>[vcvtps_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtps_s32_f32*)<br/>[vcvtps_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtps_u32_f32*)<br/>[vcvtpd_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtpd_s64_f64*)<br/>[vcvtpd_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtpd_u64_f64*)<br/></details>|
|vcvtx|Convert to lower precision,<br/> rounding to nearest with ties to odd|<details><summary>Click here to expand the API list</summary>[vcvtx_f32_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtx_f32_f64*)<br/>[vcvtx_high_f32_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtx_high_f32_f64*)<br/>[vcvtxd_f32_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtxd_f32_f64*)<br/></details>|
|vcvt_n|Convert to/from fixed point, rounding towards zero|<details><summary>Click here to expand the API list</summary>[vcvt_n_f32_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_f32_s32*)<br/>[vcvt_n_f32_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_f32_u32*)<br/>[vcvt_n_f64_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_f64_s64*)<br/>[vcvt_n_f64_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_f64_u64*)<br/>[vcvt_n_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_s32_f32*)<br/>[vcvt_n_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_s64_f64*)<br/>[vcvt_n_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_u32_f32*)<br/>[vcvt_n_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvt_n_u64_f64*)<br/>[vcvtq_n_f32_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_f32_s32*)<br/>[vcvtq_n_f32_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_f32_u32*)<br/>[vcvtq_n_f64_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_f64_s64*)<br/>[vcvtq_n_f64_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_f64_u64*)<br/>[vcvtq_n_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_s32_f32*)<br/>[vcvtq_n_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_s64_f64*)<br/>[vcvtq_n_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_u32_f32*)<br/>[vcvtq_n_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtq_n_u64_f64*)<br/>[vcvts_n_f32_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_n_f32_s32*)<br/>[vcvts_n_f32_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_n_f32_u32*)<br/>[vcvts_n_s32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_n_s32_f32*)<br/>[vcvts_n_u32_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvts_n_u32_f32*)<br/>[vcvtd_n_f64_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_n_f64_s64*)<br/>[vcvtd_n_f64_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_n_f64_u64*)<br/>[vcvtd_n_s64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_n_s64_f64*)<br/>[vcvtd_n_u64_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vcvtd_n_u64_f64*)<br/></details>|
|vrnd|Round to Integral, toward zero|<details><summary>Click here to expand the API list</summary>[vrnd_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrnd_f32*)<br/> [vrnd_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrnd_f64*)<br/> [vrndq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndq_f32*)<br/> [vrndq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndq_f64*)<br/></details>|
|vrnda|Round to Integral, with ties to away|<details><summary>Click here to expand the API list</summary>[vrnda_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrnda_f32*)<br/> [vrnda_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrnda_f64*)<br/> [vrndaq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndaq_f32*)<br/> [vrndaq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndaq_f64*)<br/></details>|
|vrndi|Round to Integral, using current rounding mode|<details><summary>Click here to expand the API list</summary>[vrndi_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndi_f32*)<br/> [vrndi_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndi_f64*)<br/> [vrndiq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndiq_f32*)<br/> [vrndiq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndiq_f64*)<br/></details>|
|vrndm|Round to Integral, towards minus infinity|<details><summary>Click here to expand the API list</summary>[vrndm_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndm_f32*)<br/> [vrndm_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndm_f64*)<br/> [vrndmq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndmq_f32*)<br/> [vrndmq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndmq_f64*)<br/></details>|
|vrndn|Round to Integral, with ties to even|<details><summary>Click here to expand the API list</summary>[vrndn_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndn_f32*)<br/> [vrndn_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndn_f64*)<br/> [vrndnq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndnq_f32*)<br/> [vrndnq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndnq_f64*)<br/> [vrndns_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndns_f32*)<br/></details>|
|vrndp|Round to Integral, towards plus infinity|<details><summary>Click here to expand the API list</summary>[vrndp_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndp_f32*)<br/> [vrndp_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndp_f64*)<br/> [vrndpq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndpq_f32*)<br/> [vrndpq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndpq_f64*)<br/></details>|
|vrndx|Round to Integral exact|<details><summary>Click here to expand the API list</summary>[vrndx_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndx_f32*)<br/> [vrndx_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndx_f64*)<br/> [vrndxq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndxq_f32*)<br/> [vrndxq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vrndxq_f64*)<br/></details>|


### Load and store

|Operation|Description|APIs|
|---|---|---|
|vld1|Load vector from memory|<details><summary>Click here to expand the API list</summary>[vld1_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_f32*)<br/>[vld1_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_f64*)<br/>[vld1_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_s16*)<br/>[vld1_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_s32*)<br/>[vld1_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_s64*)<br/>[vld1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_s8*)<br/>[vld1_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_u16*)<br/>[vld1_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_u32*)<br/>[vld1_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_u64*)<br/>[vld1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1_u8*)<br/>[vld1q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_f32*)<br/>[vld1q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_f64*)<br/>[vld1q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_s16*)<br/>[vld1q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_s32*)<br/>[vld1q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_s64*)<br/>[vld1q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_s8*)<br/>[vld1q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_u16*)<br/>[vld1q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_u32*)<br/>[vld1q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_u64*)<br/>[vld1q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vld1q_u8*)<br/></details>|
|vst1|Store vector to memory|<details><summary>Click here to expand the API list</summary>[vst1_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_f32*)<br/>[vst1_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_f64*)<br/>[vst1_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_s16*)<br/>[vst1_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_s32*)<br/>[vst1_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_s64*)<br/>[vst1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_s8*)<br/>[vst1_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_u16*)<br/>[vst1_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_u32*)<br/>[vst1_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_u64*)<br/>[vst1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1_u8*)<br/>[vst1q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_f32*)<br/>[vst1q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_f64*)<br/>[vst1q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_s16*)<br/>[vst1q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_s32*)<br/>[vst1q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_s64*)<br/>[vst1q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_s8*)<br/>[vst1q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_u16*)<br/>[vst1q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_u32*)<br/>[vst1q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_u64*)<br/>[vst1q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vst1q_u8*)<br/></details>|
|vget_lane|Get vector element|<details><summary>Click here to expand the API list</summary>[vget_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_f32*)<br/>[vget_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_f64*)<br/>[vget_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_s16*)<br/>[vget_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_s32*)<br/>[vget_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_s64*)<br/>[vget_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_s8*)<br/>[vget_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_u16*)<br/>[vget_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_u32*)<br/>[vget_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_u64*)<br/>[vget_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vget_lane_u8*)<br/>[vgetq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_f32*)<br/>[vgetq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_f64*)<br/>[vgetq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_s16*)<br/>[vgetq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_s32*)<br/>[vgetq_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_s64*)<br/>[vgetq_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_s8*)<br/>[vgetq_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_u16*)<br/>[vgetq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_u32*)<br/>[vgetq_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_u64*)<br/>[vgetq_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vgetq_lane_u8*)<br/></details>|
|vset_lane|Set vector element|<details><summary>Click here to expand the API list</summary>[vset_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_f32*)<br/>[vset_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_f64*)<br/>[vset_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_s16*)<br/>[vset_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_s32*)<br/>[vset_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_s64*)<br/>[vset_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_s8*)<br/>[vset_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_u16*)<br/>[vset_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_u32*)<br/>[vset_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_u64*)<br/>[vset_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vset_lane_u8*)<br/>[vsetq_lane_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_f32*)<br/>[vsetq_lane_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_f64*)<br/>[vsetq_lane_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_s16*)<br/>[vsetq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_s32*)<br/>[vsetq_lane_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_s64*)<br/>[vsetq_lane_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_s8*)<br/>[vsetq_lane_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_u16*)<br/>[vsetq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_u32*)<br/>[vsetq_lane_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_u64*)<br/>[vsetq_lane_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vsetq_lane_u8*)<br/></details>|

### Permutation

|Operation|Description|APIs|
|---|---|---|
|vext|Extract vector from pair of vectors|<details><summary>Click here to expand the API list</summary>[vext_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_f32*)<br/>[vext_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_f64*)<br/>[vext_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_s16*)<br/>[vext_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_s32*)<br/>[vext_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_s64*)<br/>[vext_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_s8*)<br/>[vext_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_u16*)<br/>[vext_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_u32*)<br/>[vext_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_u64*)<br/>[vext_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vext_u8*)<br/>[vextq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_f32*)<br/>[vextq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_f64*)<br/>[vextq_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_s16*)<br/>[vextq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_s32*)<br/>[vextq_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_s64*)<br/>[vextq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_s8*)<br/>[vextq_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_u16*)<br/>[vextq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_u32*)<br/>[vextq_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_u64*)<br/>[vextq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vextq_u8*)<br/></details>|
|vtbl1|Table vector Lookup|<details><summary>Click here to expand the API list</summary>[vtbl1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtbl1_s8*)<br/>[vtbl1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtbl1_u8*)<br/></details>|
|vtbx1|Table vector lookup extension|<details><summary>Click here to expand the API list</summary>[vtbx1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtbx1_s8*)<br/>[vtbx1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtbx1_u8*)<br/></details>|
|vqtbl1|Table vector Lookup|<details><summary>Click here to expand the API list</summary>[vqtbl1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbl1_s8*)<br/> [vqtbl1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbl1_u8*)<br/> [vqtbl1q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbl1q_s8*)<br/> [vqtbl1q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbl1q_u8*)<br/></details>|
|vqtbx1|Table vector lookup extension|<details><summary>Click here to expand the API list</summary>[vqtbx1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbx1_s8*)<br/> [vqtbx1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbx1_u8*)<br/> [vqtbx1q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbx1q_s8*)<br/> [vqtbx1q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vqtbx1q_u8*)<br/></details>|
|vrbit|Reverse bit order|<details><summary>Click here to expand the API list</summary>[vrbit_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrbit_s8*)<br/> [vrbit_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrbit_u8*)<br/> [vrbitq_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrbitq_s8*)<br/> [vrbitq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrbitq_u8*)<br/></details>|
|vrev16|Reverse elements in 16-bit halfwords|<details><summary>Click here to expand the API list</summary>[vrev16_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev16_s8*)<br/>[vrev16_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev16_u8*)<br/>[vrev16q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev16q_s8*)<br/>[vrev16q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev16q_u8*)<br/></details>|
|vrev32|Reverse elements in 32-bit words|<details><summary>Click here to expand the API list</summary>[vrev32_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32_s16*)<br/>[vrev32_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32_s8*)<br/>[vrev32_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32_u16*)<br/>[vrev32_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32_u8*)<br/>[vrev32q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32q_s16*)<br/>[vrev32q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32q_s8*)<br/>[vrev32q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32q_u16*)<br/>[vrev32q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev32q_u8*)<br/></details>|
|vrev64|Reverse elements in 64-bit doublewords|<details><summary>Click here to expand the API list</summary>[vrev64_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64_f32*)<br/>[vrev64_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64_s16*)<br/>[vrev64_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64_s32*)<br/>[vrev64_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64_s8*)<br/>[vrev64_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64_u16*)<br/>[vrev64_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64_u32*)<br/>[vrev64_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64_u8*)<br/>[vrev64q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64q_f32*)<br/>[vrev64q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64q_s16*)<br/>[vrev64q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64q_s32*)<br/>[vrev64q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64q_s8*)<br/>[vrev64q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64q_u16*)<br/>[vrev64q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64q_u32*)<br/>[vrev64q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vrev64q_u8*)<br/></details>|
|vtrn1|Transpose vectors (primary)|<details><summary>Click here to expand the API list</summary>[vtrn1_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1_f32*)<br/>[vtrn1_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1_s16*)<br/>[vtrn1_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1_s32*)<br/>[vtrn1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1_s8*)<br/>[vtrn1_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1_u16*)<br/>[vtrn1_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1_u32*)<br/>[vtrn1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1_u8*)<br/>[vtrn1q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_f32*)<br/>[vtrn1q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_f64*)<br/>[vtrn1q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_s16*)<br/>[vtrn1q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_s32*)<br/>[vtrn1q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_s64*)<br/>[vtrn1q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_s8*)<br/>[vtrn1q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_u16*)<br/>[vtrn1q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_u32*)<br/>[vtrn1q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_u64*)<br/>[vtrn1q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn1q_u8*)<br/></details>|
|vtrn2|Transpose vectors (secondary)|<details><summary>Click here to expand the API list</summary>[vtrn2_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2_f32*)<br/>[vtrn2_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2_s16*)<br/>[vtrn2_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2_s32*)<br/>[vtrn2_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2_s8*)<br/>[vtrn2_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2_u16*)<br/>[vtrn2_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2_u32*)<br/>[vtrn2_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2_u8*)<br/>[vtrn2q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_f32*)<br/>[vtrn2q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_f64*)<br/>[vtrn2q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_s16*)<br/>[vtrn2q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_s32*)<br/>[vtrn2q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_s64*)<br/>[vtrn2q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_s8*)<br/>[vtrn2q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_u16*)<br/>[vtrn2q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_u32*)<br/>[vtrn2q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_u64*)<br/>[vtrn2q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vtrn2q_u8*)<br/></details>|
|vzip1|Zip vectors (primary)|<details><summary>Click here to expand the API list</summary>[vzip1_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1_f32*)<br/>[vzip1_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1_s16*)<br/>[vzip1_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1_s32*)<br/>[vzip1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1_s8*)<br/>[vzip1_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1_u16*)<br/>[vzip1_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1_u32*)<br/>[vzip1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1_u8*)<br/>[vzip1q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_f32*)<br/>[vzip1q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_f64*)<br/>[vzip1q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_s16*)<br/>[vzip1q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_s32*)<br/>[vzip1q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_s64*)<br/>[vzip1q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_s8*)<br/>[vzip1q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_u16*)<br/>[vzip1q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_u32*)<br/>[vzip1q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_u64*)<br/>[vzip1q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip1q_u8*)<br/></details>|
|vzip2|Zip vectors (secondary)|<details><summary>Click here to expand the API list</summary>[vzip2_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2_f32*)<br/>[vzip2_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2_s16*)<br/>[vzip2_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2_s32*)<br/>[vzip2_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2_s8*)<br/>[vzip2_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2_u16*)<br/>[vzip2_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2_u32*)<br/>[vzip2_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2_u8*)<br/[vzip2q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_f32*)<br/>[vzip2q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_f64*)<br/>[vzip2q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_s16*)<br/>[vzip2q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_s32*)<br/>[vzip2q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_s64*)<br/>[vzip2q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_s8*)<br/>[vzip2q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_u16*)<br/>[vzip2q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_u32*)<br/>[vzip2q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_u64*)<br/>[vzip2q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vzip2q_u8*)<br/></details>|
|vuzp1|Unzip vectors (primary)|<details><summary>Click here to expand the API list</summary>[vuzp1_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1_f32*)<br/>[vuzp1_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1_s16*)<br/>[vuzp1_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1_s32*)<br/>[vuzp1_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1_s8*)<br/>[vuzp1_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1_u16*)<br/>[vuzp1_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1_u32*)<br/>[vuzp1_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1_u8*)<br/>[vuzp1q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_f32*)<br/>[vuzp1q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_f64*)<br/>[vuzp1q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_s16*)<br/>[vuzp1q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_s32*)<br/>[vuzp1q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_s64*)<br/>[vuzp1q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_s8*)<br/>[vuzp1q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_u16*)<br/>[vuzp1q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_u32*)<br/>[vuzp1q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_u64*)<br/>[vuzp1q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp1q_u8*)<br/></details>|
|vuzp2|Unzip vectors (secondary)|<details><summary>Click here to expand the API list</summary>[vuzp2_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2_f32*)<br/>[vuzp2_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2_s16*)<br/>[vuzp2_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2_s32*)<br/>[vuzp2_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2_s8*)<br/>[vuzp2_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2_u16*)<br/>[vuzp2_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2_u32*)<br/>[vuzp2_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2_u8*)<br/>[vuzp2q_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_f32*)<br/>[vuzp2q_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_f64*)<br/>[vuzp2q_s16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_s16*)<br/>[vuzp2q_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_s32*)<br/>[vuzp2q_s64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_s64*)<br/>[vuzp2q_s8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_s8*)<br/>[vuzp2q_u16](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_u16*)<br/>[vuzp2q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_u32*)<br/>[vuzp2q_u64](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_u64*)<br/>[vuzp2q_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vuzp2q_u8*)<br/></details>|

### Cryptographic

|Operation|APIs|
|---|---|
|CRC32|<details><summary>Click here to expand the API list</summary>[__crc32b](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32b*)<br/>[__crc32cb](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32cb*)<br/>[__crc32cd](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32cd*)<br/>[__crc32ch](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32ch*)<br/>[__crc32cw](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32cw*)<br/>[__crc32d](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32d*)<br/>[__crc32h](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32h*)<br/>[__crc32w](xref:Unity.Burst.Intrinsics.Arm.Neon.__crc32w*)<br/></details>|
|SHA1|<details><summary>Click here to expand the API list</summary>[vsha1cq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha1cq_u32*)<br/>[vsha1h_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha1h_u32*)<br/>[vsha1mq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha1mq_u32*)<br/>[vsha1pq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha1pq_u32*)<br/>[vsha1su0q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha1su0q_u32*)<br/>[vsha1su1q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha1su1q_u32*)<br/></details>|
|SHA256|<details><summary>Click here to expand the API list</summary>[vsha256h2q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha256h2q_u32*)<br/>[vsha256hq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha256hq_u32*)<br/>[vsha256su0q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha256su0q_u32*)<br/>[vsha256su1q_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsha256su1q_u32*)<br/></details>|
|AES|<details><summary>Click here to expand the API list</summary>[vaesdq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaesdq_u8*)<br/>[vaeseq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaeseq_u8*)<br/>[vaesimcq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaesimcq_u8*)<br/>[vaesmcq_u8](xref:Unity.Burst.Intrinsics.Arm.Neon.vaesmcq_u8*)<br/></details>|

### Miscellaneous

|Operation|Description|APIs|
|---|---|---|
|vsqrt|Square root|<details><summary>Click here to expand the API list</summary>[vsqrt_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqrt_f32*)<br/> [vsqrt_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqrt_f64*)<br/> [vsqrtq_f32](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqrtq_f32*)<br/> [vsqrtq_f64](xref:Unity.Burst.Intrinsics.Arm.Neon.vsqrtq_f64*)<br/></details>|
|vdot|Dot product|<details><summary>Click here to expand the API list</summary>[vdot_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdot_s32*)<br/> [vdot_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdot_u32*)<br/> [vdotq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdotq_s32*)<br/> [vdotq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdotq_u32*)<br/></details>|
|vdot_lane|Dot product|<details><summary>Click here to expand the API list</summary>[vdot_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdot_lane_s32*)<br/> [vdot_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdot_lane_u32*)<br/> [vdot_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdot_laneq_s32*)<br/> [vdot_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdot_laneq_u32*)<br/> [vdotq_lane_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdotq_lane_s32*)<br/> [vdotq_lane_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdotq_lane_u32*)<br/> [vdotq_laneq_s32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdotq_laneq_s32*)<br/> [vdotq_laneq_u32](xref:Unity.Burst.Intrinsics.Arm.Neon.vdotq_laneq_u32*)<br/></details>|

## Additional resources 

* [Processor specific SIMD extensions](csharp-burst-intrinsics-processors.md)