{
    "m_SGVersion": 3,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "a70fcf707e014271874f71ea685a9b64",
    "m_Properties": [],
    "m_Keywords": [],
    "m_Dropdowns": [],
    "m_CategoryData": [
        {
            "m_Id": "a4bc3c0d75ac468a881e50bf9a33dae7"
        }
    ],
    "m_Nodes": [
        {
            "m_Id": "5abff5e283f644e3ab7d2d767332f931"
        },
        {
            "m_Id": "4ef904fd245b490cabd24a6efe03f4d0"
        },
        {
            "m_Id": "5459526d1d4943558e5d5ceb28221aa8"
        },
        {
            "m_Id": "f6dca3fbec324ce6a3550e58f4b5d168"
        },
        {
            "m_Id": "ddbf96690e924eadacdac30155a17089"
        },
        {
            "m_Id": "9259c89a5b564d368d8dc13b2808dd32"
        },
        {
            "m_Id": "9ed756652bad4c809a9159b71d05e5d7"
        },
        {
            "m_Id": "30e9343db5994ff8b9252d11ef00ba32"
        },
        {
            "m_Id": "820a606f1deb46b29d3dcd34b84c8f9a"
        },
        {
            "m_Id": "04e326bc3b0c4e9ba25a1ab6348b9fb2"
        },
        {
            "m_Id": "7f1f1894a1224a3e98ec61dac56c72b2"
        },
        {
            "m_Id": "6315d3f916cb469c802383d221046a95"
        },
        {
            "m_Id": "05c971613e43447e9fed35f086de4c4a"
        },
        {
            "m_Id": "f1df6665f21e439c98f7df05aab4b773"
        },
        {
            "m_Id": "803c409474ba4a878b8a051bf9badb14"
        },
        {
            "m_Id": "ffb47db5e0fe40ae92a8edf51d2c35a9"
        },
        {
            "m_Id": "7a917417a8e24122bcf471f469cfbdee"
        },
        {
            "m_Id": "be39858cb10a48a6a83056141b92a460"
        },
        {
            "m_Id": "f62ea240ab5e4aab874f33767d2c8dc5"
        },
        {
            "m_Id": "9630af6c34f14c21a259fb93f0e678e9"
        },
        {
            "m_Id": "155c5e1abb9544ac82a8d777d84b0732"
        },
        {
            "m_Id": "2e8762c0ea1d464daef741495dce37fa"
        },
        {
            "m_Id": "4a346830384a44a782e6e351fe39b41c"
        },
        {
            "m_Id": "8284cf3176c14ddeb144718306ffe194"
        },
        {
            "m_Id": "556cee7491774385b965b3bac27d985a"
        },
        {
            "m_Id": "65e827a81b2342d8816cdb11a76f35ac"
        },
        {
            "m_Id": "e0eea81098974f8a9415004c0ac3f519"
        },
        {
            "m_Id": "66dd4dea6d774db09d681ec55bb58a01"
        },
        {
            "m_Id": "e9a04e7a29b0410082f2cb3796064c71"
        },
        {
            "m_Id": "9f5d9e187c0d4530ab6fc4ea9a89382e"
        },
        {
            "m_Id": "3708df1130b94e47902a3deccd1c3201"
        },
        {
            "m_Id": "4f979c9ec283437ab63034a084ce3a5d"
        },
        {
            "m_Id": "883dacc0d5eb43fc81ede3d68d3ecf3c"
        },
        {
            "m_Id": "c039fd517fdd4eaca7869e4d9e7f5e62"
        },
        {
            "m_Id": "7899c8939cfe433b84e7eb65a2410933"
        },
        {
            "m_Id": "27820c19355845a59402e26a12fa3ede"
        },
        {
            "m_Id": "689f60b01e424fb79935b92e4d65bff2"
        },
        {
            "m_Id": "ef4f7e32776c433991c077bce7dbfce3"
        },
        {
            "m_Id": "eff67640ac57406b82f343f1574be2f7"
        },
        {
            "m_Id": "269332ec2ebd41089868a0003013f81d"
        },
        {
            "m_Id": "426cd6e171fe41c2bbf8adb2a1957394"
        },
        {
            "m_Id": "453f87541d61429bbc8f6c1f837bdc73"
        },
        {
            "m_Id": "cbac6445fa2c45ac9bdc2bd9712169d4"
        },
        {
            "m_Id": "807ef4cf2e164175805bdfff9a48de98"
        },
        {
            "m_Id": "a642851d4a914b1e99e197091b271113"
        },
        {
            "m_Id": "a0cc344958f14f1c8cd0f583f231763b"
        },
        {
            "m_Id": "fd313955e8ab474ca1beda45c1a6a27d"
        },
        {
            "m_Id": "271ec042ed8e4454a8e3e7d5aa3149c5"
        },
        {
            "m_Id": "27232e5910bf435695530de187c616b4"
        },
        {
            "m_Id": "c52bfbf731c04b9d8fb2d2ecc4e92309"
        },
        {
            "m_Id": "f9c71c5189ce4912a2293fbba9a7b31c"
        },
        {
            "m_Id": "68fff00f775948699fd5f8e275c63dd0"
        },
        {
            "m_Id": "5cb551d3244b43319a26c4e39cf1272d"
        },
        {
            "m_Id": "efd1c09399d64609ab01094c21b10db3"
        },
        {
            "m_Id": "fad0dc48a0974df2bf9587aaa8c841d2"
        },
        {
            "m_Id": "e0e771cc866b4d36809d98aeed0e3931"
        },
        {
            "m_Id": "00bd4209e1b84c1bb2c8505303e91768"
        },
        {
            "m_Id": "e99f0907e6c941c89c9c7bf5c8cc0883"
        }
    ],
    "m_GroupDatas": [
        {
            "m_Id": "bf7e0d211350434993a2fe4fd8a69fec"
        },
        {
            "m_Id": "02b4f2070ec54739a9427c41ef11eac3"
        },
        {
            "m_Id": "bc2702169ebf48c88fc96efb659ec121"
        },
        {
            "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
        },
        {
            "m_Id": "4e9adb178a834c5c81430c79ede15533"
        },
        {
            "m_Id": "b21b88f416364377b4b17ecf6148bb07"
        },
        {
            "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
        },
        {
            "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
        },
        {
            "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
        },
        {
            "m_Id": "83ae06f5792b4f9f8b6265b446e82cff"
        }
    ],
    "m_StickyNoteDatas": [
        {
            "m_Id": "afb24548dad74d8e8ee270fe888555fb"
        },
        {
            "m_Id": "af8d41599c6e42fda5c05a0a7f74bae6"
        },
        {
            "m_Id": "4c6f5de304544892903ef923556e848d"
        },
        {
            "m_Id": "9c9d538994cb48158f706724c87141e0"
        },
        {
            "m_Id": "5ee7e355fcb5436da7821048c4c6d6ea"
        },
        {
            "m_Id": "5e36f6a71b7446559cca3f57ab577b00"
        },
        {
            "m_Id": "25cf951831c945b993f7a4624ec00d4c"
        },
        {
            "m_Id": "118ebb925a86444b96670bacae83e5d8"
        },
        {
            "m_Id": "a5b563431c914cc4b3128ce755be69e2"
        },
        {
            "m_Id": "7427feac96b34198a1029e63e2882b9d"
        },
        {
            "m_Id": "740eb2cf1c9c4a7db374b115887e6c25"
        },
        {
            "m_Id": "f6b545ccbd2147ba96082dd18f66bdba"
        },
        {
            "m_Id": "17f492d3f4f949648de87f69440fdee4"
        },
        {
            "m_Id": "761d5a85493d4971a643e1b4b378f1a2"
        },
        {
            "m_Id": "b820ea4f4fd243428a4320ed66780ec0"
        },
        {
            "m_Id": "69fc5a174dfc437bb07e6b092389d011"
        },
        {
            "m_Id": "16ed7a2695624f438fc05ce86563ffee"
        },
        {
            "m_Id": "594e178b392a4f299a36a0ded75e5fef"
        },
        {
            "m_Id": "ea199312bbfc4b6ba8af869e60e80198"
        },
        {
            "m_Id": "5afcbb0890e642ce84f328f5a19adbdf"
        },
        {
            "m_Id": "02e4d2df02424dce9c6547cbda196c52"
        }
    ],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "04e326bc3b0c4e9ba25a1ab6348b9fb2"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7f1f1894a1224a3e98ec61dac56c72b2"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "05c971613e43447e9fed35f086de4c4a"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f1df6665f21e439c98f7df05aab4b773"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "155c5e1abb9544ac82a8d777d84b0732"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e0eea81098974f8a9415004c0ac3f519"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "269332ec2ebd41089868a0003013f81d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "426cd6e171fe41c2bbf8adb2a1957394"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "27232e5910bf435695530de187c616b4"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "271ec042ed8e4454a8e3e7d5aa3149c5"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "27820c19355845a59402e26a12fa3ede"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "689f60b01e424fb79935b92e4d65bff2"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "27820c19355845a59402e26a12fa3ede"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7899c8939cfe433b84e7eb65a2410933"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "2e8762c0ea1d464daef741495dce37fa"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "4a346830384a44a782e6e351fe39b41c"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "30e9343db5994ff8b9252d11ef00ba32"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "820a606f1deb46b29d3dcd34b84c8f9a"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "426cd6e171fe41c2bbf8adb2a1957394"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "453f87541d61429bbc8f6c1f837bdc73"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "453f87541d61429bbc8f6c1f837bdc73"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "cbac6445fa2c45ac9bdc2bd9712169d4"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4a346830384a44a782e6e351fe39b41c"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "8284cf3176c14ddeb144718306ffe194"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "4f979c9ec283437ab63034a084ce3a5d"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3708df1130b94e47902a3deccd1c3201"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "556cee7491774385b965b3bac27d985a"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "8284cf3176c14ddeb144718306ffe194"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5cb551d3244b43319a26c4e39cf1272d"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c52bfbf731c04b9d8fb2d2ecc4e92309"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6315d3f916cb469c802383d221046a95"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "05c971613e43447e9fed35f086de4c4a"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "66dd4dea6d774db09d681ec55bb58a01"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e9a04e7a29b0410082f2cb3796064c71"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "689f60b01e424fb79935b92e4d65bff2"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3708df1130b94e47902a3deccd1c3201"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7899c8939cfe433b84e7eb65a2410933"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "3708df1130b94e47902a3deccd1c3201"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7a917417a8e24122bcf471f469cfbdee"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "155c5e1abb9544ac82a8d777d84b0732"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "803c409474ba4a878b8a051bf9badb14"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "ffb47db5e0fe40ae92a8edf51d2c35a9"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8284cf3176c14ddeb144718306ffe194"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "65e827a81b2342d8816cdb11a76f35ac"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "883dacc0d5eb43fc81ede3d68d3ecf3c"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "c039fd517fdd4eaca7869e4d9e7f5e62"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "9259c89a5b564d368d8dc13b2808dd32"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "30e9343db5994ff8b9252d11ef00ba32"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "9630af6c34f14c21a259fb93f0e678e9"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "155c5e1abb9544ac82a8d777d84b0732"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "9ed756652bad4c809a9159b71d05e5d7"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "30e9343db5994ff8b9252d11ef00ba32"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a0cc344958f14f1c8cd0f583f231763b"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "271ec042ed8e4454a8e3e7d5aa3149c5"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "a642851d4a914b1e99e197091b271113"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fd313955e8ab474ca1beda45c1a6a27d"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "be39858cb10a48a6a83056141b92a460"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "f62ea240ab5e4aab874f33767d2c8dc5"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c039fd517fdd4eaca7869e4d9e7f5e62"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "27820c19355845a59402e26a12fa3ede"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c039fd517fdd4eaca7869e4d9e7f5e62"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "4f979c9ec283437ab63034a084ce3a5d"
                },
                "m_SlotId": 3
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "c52bfbf731c04b9d8fb2d2ecc4e92309"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "68fff00f775948699fd5f8e275c63dd0"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "cbac6445fa2c45ac9bdc2bd9712169d4"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "807ef4cf2e164175805bdfff9a48de98"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e0eea81098974f8a9415004c0ac3f519"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2e8762c0ea1d464daef741495dce37fa"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "e9a04e7a29b0410082f2cb3796064c71"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "9f5d9e187c0d4530ab6fc4ea9a89382e"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ef4f7e32776c433991c077bce7dbfce3"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "eff67640ac57406b82f343f1574be2f7"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "efd1c09399d64609ab01094c21b10db3"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "fad0dc48a0974df2bf9587aaa8c841d2"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "eff67640ac57406b82f343f1574be2f7"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "426cd6e171fe41c2bbf8adb2a1957394"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "f62ea240ab5e4aab874f33767d2c8dc5"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "9630af6c34f14c21a259fb93f0e678e9"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fad0dc48a0974df2bf9587aaa8c841d2"
                },
                "m_SlotId": 3
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "e0e771cc866b4d36809d98aeed0e3931"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "fd313955e8ab474ca1beda45c1a6a27d"
                },
                "m_SlotId": 2
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "a0cc344958f14f1c8cd0f583f231763b"
                },
                "m_SlotId": 0
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "ffb47db5e0fe40ae92a8edf51d2c35a9"
                },
                "m_SlotId": 1
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "7a917417a8e24122bcf471f469cfbdee"
                },
                "m_SlotId": 0
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 0.0,
            "y": 0.0
        },
        "m_Blocks": [
            {
                "m_Id": "5abff5e283f644e3ab7d2d767332f931"
            },
            {
                "m_Id": "4ef904fd245b490cabd24a6efe03f4d0"
            },
            {
                "m_Id": "5459526d1d4943558e5d5ceb28221aa8"
            }
        ]
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 0.0,
            "y": 200.0
        },
        "m_Blocks": [
            {
                "m_Id": "f6dca3fbec324ce6a3550e58f4b5d168"
            },
            {
                "m_Id": "00bd4209e1b84c1bb2c8505303e91768"
            },
            {
                "m_Id": "e99f0907e6c941c89c9c7bf5c8cc0883"
            }
        ]
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        },
        "preventRotation": false
    },
    "m_Path": "Shader Graphs",
    "m_GraphPrecision": 1,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": ""
    },
    "m_SubDatas": [],
    "m_ActiveTargets": [
        {
            "m_Id": "054ddf41c0de44cc901c1ef9f8a95768"
        },
        {
            "m_Id": "59cf826a6c294cb19669de252648c2d9"
        },
        {
            "m_Id": "f0d8c595ab14488d9b0414fee46c663c"
        }
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0028d0dceae743d889ff1f03256c0c66",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0034cd5d8c074009a70ca2c267b23750",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "007286d5e6e441d29167de4c0d2edce8",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "00bd4209e1b84c1bb2c8505303e91768",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Emission",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1984c05359c44d5e8fe42fc20ae01853"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Emission"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "02b4f2070ec54739a9427c41ef11eac3",
    "m_Title": "Time Loops",
    "m_Position": {
        "x": -1209.00048828125,
        "y": -36.499916076660159
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "02e4d2df02424dce9c6547cbda196c52",
    "m_Title": "",
    "m_Content": "Using Time as the Rotation input, you can create rotating texture effects.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -375.5000305175781,
        "y": 1970.0001220703125,
        "width": 200.00001525878907,
        "height": 100.0001220703125
    },
    "m_Group": {
        "m_Id": "83ae06f5792b4f9f8b6265b446e82cff"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "047ecf1a501e4602a885871d02449585",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "04e326bc3b0c4e9ba25a1ab6348b9fb2",
    "m_Group": {
        "m_Id": "02b4f2070ec54739a9427c41ef11eac3"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1184.000244140625,
            "y": 22.000001907348634,
            "width": 79.0001220703125,
            "height": 76.00006866455078
        }
    },
    "m_Slots": [
        {
            "m_Id": "0fdf89411bea46b6aec827b54915914a"
        },
        {
            "m_Id": "441adf6dcea54878a587f30627c0e872"
        },
        {
            "m_Id": "18a35d18091f465788c3a7b807dbdfea"
        },
        {
            "m_Id": "a734f72edee149329517a97b9315e00a"
        },
        {
            "m_Id": "73ac945633aa4b5889019e39b4e44f08"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInTarget",
    "m_ObjectId": "054ddf41c0de44cc901c1ef9f8a95768",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "30dca1d138ad4384a948468f14c75ad6"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZWriteControl": 0,
    "m_ZTestMode": 4,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CustomEditorGUI": ""
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "05683763997141609f359cc4d294fb00",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "05bf5a1b4ded4c548049d71e16c9dcbf",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "05c971613e43447e9fed35f086de4c4a",
    "m_Group": {
        "m_Id": "bc2702169ebf48c88fc96efb659ec121"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -621.9999389648438,
            "y": 21.500049591064454,
            "width": 125.99993896484375,
            "height": 117.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "59ab4007211b46219d92cff08315c21b"
        },
        {
            "m_Id": "d3634b4f576d4601bf7fb3816be36a14"
        },
        {
            "m_Id": "4d33f5ab2f6c4cdd84d3cc04ec20a059"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "063db92972f643cabfcb708a143f78fd",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "06faa2cf629e4f919ce32bbba71670ad",
    "m_Title": "Animated Dissolve Effect",
    "m_Position": {
        "x": -1811.5,
        "y": 2118.5
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "09bf60ae65184af69f92502d5a0525d1",
    "m_Id": 3,
    "m_DisplayName": "Frequency",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Frequency",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 7.0,
        "y": 7.0
    },
    "m_DefaultValue": {
        "x": 1.0,
        "y": 1.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "0a804920bb754e9eb266ae972b947a47",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "0f62e50383014dbdad9a4ab475238a07",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0fc2bbb1ce5c44ba964379f36fba8a0d",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0fdf89411bea46b6aec827b54915914a",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "118ebb925a86444b96670bacae83e5d8",
    "m_Title": "",
    "m_Content": "Then we increase the contrast and expand the range to 5 * PI.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -798.0000610351563,
        "y": 1419.0001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.BuiltinData",
    "m_ObjectId": "13c5b6aec7e74afe82564b0f133b6686",
    "m_Distortion": false,
    "m_DistortionMode": 0,
    "m_DistortionDepthTest": true,
    "m_AddPrecomputedVelocity": false,
    "m_TransparentWritesMotionVec": false,
    "m_DepthOffset": false,
    "m_ConservativeDepthOffset": false,
    "m_TransparencyFog": true,
    "m_AlphaTestShadow": false,
    "m_BackThenFrontRendering": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_TransparentPerPixelSorting": false,
    "m_SupportLodCrossFade": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "13fa792192b34935a784e6592cecfdd8",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "154a057b390849c993afc7c15ea4c8b6",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AddNode",
    "m_ObjectId": "155c5e1abb9544ac82a8d777d84b0732",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Add",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1258.5001220703125,
            "y": 1128.0001220703125,
            "width": 208.0001220703125,
            "height": 302.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "25e056b147574a6c9df286a4f51ec843"
        },
        {
            "m_Id": "52334a02d83d420f924f58313d4c5db3"
        },
        {
            "m_Id": "f63090608aa841298a1dd727ccd45155"
        }
    ],
    "synonyms": [
        "addition",
        "sum",
        "plus"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "163fbab7e8704032b677f26c9c59f3a5",
    "m_Id": 2,
    "m_DisplayName": "Y",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Y",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Y"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "16ed7a2695624f438fc05ce86563ffee",
    "m_Title": "",
    "m_Content": "The Fade Transition node creates a disolve effect using the input noise texture and the Fade Value.  The Fade Contrast value can use used to controll the sharpness of the edges of the fade.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1302.0001220703125,
        "y": 2516.000244140625,
        "width": 200.0,
        "height": 116.0
    },
    "m_Group": {
        "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "17f492d3f4f949648de87f69440fdee4",
    "m_Title": "",
    "m_Content": "Using the Sine of Time to create a cycling animated value.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1783.5001220703125,
        "y": 1905.0001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "18a35d18091f465788c3a7b807dbdfea",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "1984c05359c44d5e8fe42fc20ae01853",
    "m_Id": 0,
    "m_DisplayName": "Emission",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Emission",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 1,
    "m_DefaultColor": {
        "r": 0.0,
        "g": 0.0,
        "b": 0.0,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "19e8bca458d94517b402cb765e739901",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1b4e868785004c479f4710f919d729f7",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1c3b6614bf7d450a9a1b427064648cfe",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "1f05c446661a4251b0a6ea183d22532b",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"b8aa35b1f4ce5ee5bbf0376cb9eb10cb\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "1f2ac81d4ef04c6984da4f8920bb7033",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "1f84d4e3531c42df8969d49496d7649a",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "20fe509050654cd689a7d51981b35c54",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "216b29732a1e4c958ebd568628b8898f",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "237d9397f36641c98467b328b11e85f7",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "25cf951831c945b993f7a4624ec00d4c",
    "m_Title": "",
    "m_Content": "Then we animate it by adding time.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1263.0001220703125,
        "y": 1436.5001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "25e056b147574a6c9df286a4f51ec843",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "269332ec2ebd41089868a0003013f81d",
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1545.0001220703125,
            "y": 1656.000244140625,
            "width": 206.0,
            "height": 130.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "e660441d79ad463781b3c84b89ed01f1"
        }
    ],
    "synonyms": [
        "location"
    ],
    "m_Precision": 1,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 0,
    "m_PositionSource": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FadeTransitionNode",
    "m_ObjectId": "271ec042ed8e4454a8e3e7d5aa3149c5",
    "m_Group": {
        "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
    },
    "m_Name": "Fade Transition",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1306.500244140625,
            "y": 2177.000244140625,
            "width": 208.0001220703125,
            "height": 325.999755859375
        }
    },
    "m_Slots": [
        {
            "m_Id": "a94a057607314147b1c60135fc79f9ff"
        },
        {
            "m_Id": "b6c3520dac914c15b70a25e9fd32af0f"
        },
        {
            "m_Id": "f990e1cd211f4eeda0a0b67dee086272"
        },
        {
            "m_Id": "d740e42f79bf43cc8e3c97b97b020a74"
        }
    ],
    "synonyms": [
        "fade"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "27232e5910bf435695530de187c616b4",
    "m_Group": {
        "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1638.000244140625,
            "y": 2177.000244140625,
            "width": 208.0001220703125,
            "height": 338.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8a768a35cca345d3a6c3e864535ffb87"
        },
        {
            "m_Id": "e9b9ad8ffe3e405589d8e870e29e4c92"
        },
        {
            "m_Id": "f87f6ead74f749aa96ecb583c4f95a3d"
        },
        {
            "m_Id": "da85bac33cf94aa4abb6b4b1c8ba90e9"
        },
        {
            "m_Id": "60e3eb3e15bf45c9973712aa1571a603"
        },
        {
            "m_Id": "48b32cf9f2df46d98cabbd7561a75546"
        },
        {
            "m_Id": "f978ac0702854acc8a3ef83cd8d3ccfd"
        },
        {
            "m_Id": "3a0ea4db54504c31b6ce39a589137148"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FractionNode",
    "m_ObjectId": "27820c19355845a59402e26a12fa3ede",
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    },
    "m_Name": "Fraction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -935.5,
            "y": 717.5000610351563,
            "width": 127.49981689453125,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1f84d4e3531c42df8969d49496d7649a"
        },
        {
            "m_Id": "ae579837e5204b329ea0652adfc1e10f"
        }
    ],
    "synonyms": [
        "remainder"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "27d437bd201e4bfa93e64b6a8ef2a06d",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "284cab90429c443ba94d4e341979e960",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "29298eec456e4cc89244c2468a6d471a",
    "m_Id": 2,
    "m_DisplayName": "Radial Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "RadialScale",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2ad51adac6ec4fc28109b09fc47df873",
    "m_Id": 2,
    "m_DisplayName": "Strength",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Strength",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 10.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "2c01faf27f7a4b748d7584c34a621fa7",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2cf953f7046545e8beffe199df7d4a8e",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "2d08bb9782b84c35b8cc8e23c919a77a",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "2e4bfb8c17d444e19d130ea23f00f902",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "2e8762c0ea1d464daef741495dce37fa",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -835.500244140625,
            "y": 1128.0001220703125,
            "width": 126.0,
            "height": 118.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "0f62e50383014dbdad9a4ab475238a07"
        },
        {
            "m_Id": "b9cbae335ad3478cb9d0cfeca317a7df"
        },
        {
            "m_Id": "71b413eeacd649578a05a40b5250276f"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitData",
    "m_ObjectId": "2f00130a9de54061b8de1840ee0f2422",
    "m_EnableShadowMatte": false,
    "m_DistortionOnly": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "305b6d5d1f254246bcecf5821c3d2b1b",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.BuiltIn.ShaderGraph.BuiltInUnlitSubTarget",
    "m_ObjectId": "30dca1d138ad4384a948468f14c75ad6"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AddNode",
    "m_ObjectId": "30e9343db5994ff8b9252d11ef00ba32",
    "m_Group": {
        "m_Id": "bf7e0d211350434993a2fe4fd8a69fec"
    },
    "m_Name": "Add",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1602.5001220703125,
            "y": 21.00001335144043,
            "width": 129.4998779296875,
            "height": 118.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "d8eeef681c7f488ab5eacc23f07ac4aa"
        },
        {
            "m_Id": "d5d5c60e46574d509725bc43729491f7"
        },
        {
            "m_Id": "48cbfa0754d5411196000d0c4e91240b"
        }
    ],
    "synonyms": [
        "addition",
        "sum",
        "plus"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "316e192f54054f62b2717be01b1eee6b",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "318ee9f4873646139583a26302d430eb",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "328456c6cfbd40498ae78b8617fdd368",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3322d89a835d4c359c975c852725c518",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3418ebd5741a4104b5e30b5910d07f2b",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CheckerboardNode",
    "m_ObjectId": "3708df1130b94e47902a3deccd1c3201",
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    },
    "m_Name": "Checkerboard",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -541.5,
            "y": 502.5000915527344,
            "width": 207.99981689453126,
            "height": 350.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "2d08bb9782b84c35b8cc8e23c919a77a"
        },
        {
            "m_Id": "af74d331682344a88e3a5dd0c1b5729e"
        },
        {
            "m_Id": "6cc75dbc8da74a1c9f91a9fc44030e31"
        },
        {
            "m_Id": "09bf60ae65184af69f92502d5a0525d1"
        },
        {
            "m_Id": "7ecd825bc5974367abb516171c6bf27c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "381f11c735524960a0bd7759ec022177",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "39b2f23265134506bc876a96137c9add",
    "m_Id": 0,
    "m_DisplayName": "Alpha",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Alpha",
    "m_StageCapability": 2,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "3a0ea4db54504c31b6ce39a589137148",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GradientInputMaterialSlot",
    "m_ObjectId": "3b3a1ac8fdbb422b928a1db8ba50cbd3",
    "m_Id": 0,
    "m_DisplayName": "Gradient",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Gradient",
    "m_StageCapability": 3,
    "m_Value": {
        "serializedVersion": "2",
        "key0": {
            "r": 1.0,
            "g": 0.3333333432674408,
            "b": 0.0,
            "a": 1.0
        },
        "key1": {
            "r": 0.2999997138977051,
            "g": 0.0,
            "b": 1.0,
            "a": 1.0
        },
        "key2": {
            "r": 1.0,
            "g": 0.3333333432674408,
            "b": 0.0,
            "a": 0.0
        },
        "key3": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key4": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key5": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key6": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key7": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "ctime0": 0,
        "ctime1": 32767,
        "ctime2": 65535,
        "ctime3": 0,
        "ctime4": 0,
        "ctime5": 0,
        "ctime6": 0,
        "ctime7": 0,
        "atime0": 0,
        "atime1": 65535,
        "atime2": 0,
        "atime3": 0,
        "atime4": 0,
        "atime5": 0,
        "atime6": 0,
        "atime7": 0,
        "m_Mode": 0,
        "m_ColorSpace": -1,
        "m_NumColorKeys": 3,
        "m_NumAlphaKeys": 2
    },
    "m_DefaultValue": {
        "serializedVersion": "2",
        "key0": {
            "r": 1.0,
            "g": 1.0,
            "b": 1.0,
            "a": 1.0
        },
        "key1": {
            "r": 1.0,
            "g": 1.0,
            "b": 1.0,
            "a": 1.0
        },
        "key2": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key3": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key4": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key5": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key6": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key7": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "ctime0": 0,
        "ctime1": 65535,
        "ctime2": 0,
        "ctime3": 0,
        "ctime4": 0,
        "ctime5": 0,
        "ctime6": 0,
        "ctime7": 0,
        "atime0": 0,
        "atime1": 65535,
        "atime2": 0,
        "atime3": 0,
        "atime4": 0,
        "atime5": 0,
        "atime6": 0,
        "atime7": 0,
        "m_Mode": 0,
        "m_ColorSpace": -1,
        "m_NumColorKeys": 2,
        "m_NumAlphaKeys": 2
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3c55cc25a62545e096fb9694d72c41bc",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3d450ff1b3ca4245902d11f83f665298",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3ea2f7b83f3a46e5925d557246d402e4",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "403bf57b66064287a89dca669eb940c6",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "404fb29806cb4de8a2570dc1cf062796",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4163a688f3fd4270814a62181dde8b43",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "41ddf12ddb634f9aa3370d70d774c0d6",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DistanceNode",
    "m_ObjectId": "426cd6e171fe41c2bbf8adb2a1957394",
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    },
    "m_Name": "Distance",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1223.000244140625,
            "y": 1717.5001220703125,
            "width": 127.500244140625,
            "height": 118.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "e021bdd7648445a0a9f8f999634dd7db"
        },
        {
            "m_Id": "1c3b6614bf7d450a9a1b427064648cfe"
        },
        {
            "m_Id": "c71bea4ad39e4fe0892a118dddd81a0a"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.SystemData",
    "m_ObjectId": "42ba1209114c424fa37a8b4923bbb927",
    "m_MaterialNeedsUpdateHash": 0,
    "m_SurfaceType": 0,
    "m_RenderingPass": 1,
    "m_BlendMode": 0,
    "m_ZTest": 4,
    "m_ZWrite": false,
    "m_TransparentCullMode": 2,
    "m_OpaqueCullMode": 2,
    "m_SortPriority": 0,
    "m_AlphaTest": false,
    "m_TransparentDepthPrepass": false,
    "m_TransparentDepthPostpass": false,
    "m_SupportLodCrossFade": false,
    "m_DoubleSidedMode": 0,
    "m_DOTSInstancing": false,
    "m_CustomVelocity": false,
    "m_Tessellation": false,
    "m_TessellationMode": 0,
    "m_TessellationFactorMinDistance": 20.0,
    "m_TessellationFactorMaxDistance": 50.0,
    "m_TessellationFactorTriangleSize": 100.0,
    "m_TessellationShapeFactor": 0.75,
    "m_TessellationBackFaceCullEpsilon": -0.25,
    "m_TessellationMaxDisplacement": 0.009999999776482582,
    "m_DebugSymbols": false,
    "m_Version": 2,
    "inspectorFoldoutMask": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4309b9f7515e4a679d7a487671eaec4a",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "441adf6dcea54878a587f30627c0e872",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "453f87541d61429bbc8f6c1f837bdc73",
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1016.500244140625,
            "y": 1717.5001220703125,
            "width": 127.5001220703125,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f28ec1f18b41482e8f478cadcb37fc31"
        },
        {
            "m_Id": "e0fdb9ec65a247c798b95e9d596b8c3a"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4636548462e84e588de4cceb2c65b4f3",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "48b32cf9f2df46d98cabbd7561a75546",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"5f55be20229195447a95cd4c8022a5ce\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "48cbfa0754d5411196000d0c4e91240b",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MinimumNode",
    "m_ObjectId": "4a346830384a44a782e6e351fe39b41c",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Minimum",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -686.0001831054688,
            "y": 1176.0001220703125,
            "width": 126.0,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "5f2ee9c3252d4aa7a34a33a80fc3c192"
        },
        {
            "m_Id": "4ef3158cf138423baf43c323aa786227"
        },
        {
            "m_Id": "da9d05b7f0e44b42bc0545bca0c1f476"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "4c6f5de304544892903ef923556e848d",
    "m_Title": "",
    "m_Content": "Using a Fraction node, you can turn Time into a value that goes from zero to one and then jumps back to zero again - a continous loop.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1083.5001220703125,
        "y": 308.0000305175781,
        "width": 200.00006103515626,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "02b4f2070ec54739a9427c41ef11eac3"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "4d33f5ab2f6c4cdd84d3cc04ec20a059",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "4e9adb178a834c5c81430c79ede15533",
    "m_Title": "Bouncing",
    "m_Position": {
        "x": -1795.0001220703125,
        "y": 444.00018310546877
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "4eaf9dc379ee45b692c56cd839f22ff6",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "4ef3158cf138423baf43c323aa786227",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 5.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "4ef904fd245b490cabd24a6efe03f4d0",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Normal",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9d2575bb808d45bb9a48357a59ca2944"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Normal"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TwirlNode",
    "m_ObjectId": "4f979c9ec283437ab63034a084ce3a5d",
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    },
    "m_Name": "Twirl",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -807.500244140625,
            "y": 502.5000915527344,
            "width": 153.5,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "c325f3cd9a7c434a96b26960b315761b"
        },
        {
            "m_Id": "95b681eecd4045ea964e0e51d3e56da2"
        },
        {
            "m_Id": "2ad51adac6ec4fc28109b09fc47df873"
        },
        {
            "m_Id": "ceb08644c41f4ec884e94474a4e07b79"
        },
        {
            "m_Id": "1f2ac81d4ef04c6984da4f8920bb7033"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5023c4c2282343f488e6efdde0cf7913",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "51e23f16bb424ebf8f52d2d6403caafb",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "52334a02d83d420f924f58313d4c5db3",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "5459526d1d4943558e5d5ceb28221aa8",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Tangent",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c259fb0568ea4db39df97f01128b0e08"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Tangent"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ConstantNode",
    "m_ObjectId": "556cee7491774385b965b3bac27d985a",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Constant",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -705.0,
            "y": 1294.0001220703125,
            "width": 144.99981689453126,
            "height": 111.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "a6fec41608fe48f0bed52ebca23bd41d"
        }
    ],
    "synonyms": [
        "pi",
        "tau",
        "phi"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_constant": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "594e178b392a4f299a36a0ded75e5fef",
    "m_Title": "",
    "m_Content": "Here we have a texture that's arranged in a grid with 16 frames.\n\nYou could render out a complex effect such as an explosion, or animated smoke, arrange the frames in a grid, and then play them back using the Flipbook node. Visually, the results are similar, but the cost is reduced by orders of magnitude.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1015.5000610351563,
        "y": 2498.500244140625,
        "width": 200.0,
        "height": 169.5
    },
    "m_Group": {
        "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "5989759020bf4e189ccdd8204992bff5",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "59ab4007211b46219d92cff08315c21b",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDTarget",
    "m_ObjectId": "59cf826a6c294cb19669de252648c2d9",
    "m_ActiveSubTarget": {
        "m_Id": "5a81110c1d8949a29726abfc3e27e9de"
    },
    "m_Datas": [
        {
            "m_Id": "13c5b6aec7e74afe82564b0f133b6686"
        },
        {
            "m_Id": "42ba1209114c424fa37a8b4923bbb927"
        },
        {
            "m_Id": "2f00130a9de54061b8de1840ee0f2422"
        }
    ],
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false,
    "m_SupportLineRendering": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.Rendering.HighDefinition.ShaderGraph.HDUnlitSubTarget",
    "m_ObjectId": "5a81110c1d8949a29726abfc3e27e9de"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "5abff5e283f644e3ab7d2d767332f931",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "VertexDescription.Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9a1ac83b50064ed3b72f106a2edc920e"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "VertexDescription.Position"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "5afcbb0890e642ce84f328f5a19adbdf",
    "m_Title": "",
    "m_Content": "The Tile input is the frame number to display.  In this case it has a range of 0-15 because there are 4 rows and 4 columns for a total of 16 frames.\n\nIf we plug Time into the Tile input port, we can flip from one frame to the next - creating an animated effect.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -750.0000610351563,
        "y": 2436.000244140625,
        "width": 277.0000305175781,
        "height": 117.0
    },
    "m_Group": {
        "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "5cb551d3244b43319a26c4e39cf1272d",
    "m_Group": {
        "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -775.4999389648438,
            "y": 2347.500244140625,
            "width": 78.999755859375,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0034cd5d8c074009a70ca2c267b23750"
        },
        {
            "m_Id": "e9097f29f29a48c1914f4219087ad18e"
        },
        {
            "m_Id": "b8e3d496cba04e7f91d02e460bf0c10c"
        },
        {
            "m_Id": "41ddf12ddb634f9aa3370d70d774c0d6"
        },
        {
            "m_Id": "3ea2f7b83f3a46e5925d557246d402e4"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "5e36f6a71b7446559cca3f57ab577b00",
    "m_Title": "",
    "m_Content": "First we create a radial gradient with the Polar Coordinates node.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1753.0001220703125,
        "y": 1344.5001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5e3b2ffb41e04b6f804862c79b07ce28",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "5ee7e355fcb5436da7821048c4c6d6ea",
    "m_Title": "",
    "m_Content": "Finally, we use the Sine node to convert the larger range into 3 ripples or waves.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -392.5000305175781,
        "y": 1457.0001220703125,
        "width": 200.00001525878907,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "5f2ee9c3252d4aa7a34a33a80fc3c192",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "600ddd7ef952465aaeaf767544216012",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "60291e712d944009a7b7b6096a406eee",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalUnlitSubTarget",
    "m_ObjectId": "60b1d50a561a46fe8740b78eb29842cd"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "60e3eb3e15bf45c9973712aa1571a603",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6260951ba20b44a491a76e771b721d84",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "6315d3f916cb469c802383d221046a95",
    "m_Group": {
        "m_Id": "bc2702169ebf48c88fc96efb659ec121"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -821.500244140625,
            "y": 21.500049591064454,
            "width": 104.50006103515625,
            "height": 75.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "316e192f54054f62b2717be01b1eee6b"
        },
        {
            "m_Id": "7c945ac586004df4a6109e82cbb688a7"
        },
        {
            "m_Id": "1b4e868785004c479f4710f919d729f7"
        },
        {
            "m_Id": "c75b799937d9464e8bfaea96d4c2fe48"
        },
        {
            "m_Id": "4163a688f3fd4270814a62181dde8b43"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "6317bc3d66734be7a1878c86b42d9b0c",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "650e5e1f9f2740a8adaf8c157c2aff14",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SineNode",
    "m_ObjectId": "65e827a81b2342d8816cdb11a76f35ac",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Sine",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -391.0000305175781,
            "y": 1176.0001220703125,
            "width": 208.0001220703125,
            "height": 278.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "305b6d5d1f254246bcecf5821c3d2b1b"
        },
        {
            "m_Id": "d5ded0338f544274b411e090f5f3e814"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "66267aeff6fd46ccbf6d08e09f3a1072",
    "m_Id": 1,
    "m_DisplayName": "Time",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "66dd4dea6d774db09d681ec55bb58a01",
    "m_Group": {
        "m_Id": "4e9adb178a834c5c81430c79ede15533"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1770.0001220703125,
            "y": 502.5000915527344,
            "width": 79.0,
            "height": 75.99990844726563
        }
    },
    "m_Slots": [
        {
            "m_Id": "a691c14bdedd4a5d8146f0f840e88cbe"
        },
        {
            "m_Id": "19e8bca458d94517b402cb765e739901"
        },
        {
            "m_Id": "e015519de9144c2d9f473d4a4723c3ad"
        },
        {
            "m_Id": "a0fc3ec46ac347fa8159015947ca0314"
        },
        {
            "m_Id": "0028d0dceae743d889ff1f03256c0c66"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.SampleGradient",
    "m_ObjectId": "689f60b01e424fb79935b92e4d65bff2",
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    },
    "m_Name": "Sample Gradient",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -753.5000610351563,
            "y": 764.5000610351563,
            "width": 167.99993896484376,
            "height": 118.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "c1cac5fae26f4d1da8e87ebb08c2d1d3"
        },
        {
            "m_Id": "66267aeff6fd46ccbf6d08e09f3a1072"
        },
        {
            "m_Id": "ff2f98b894ba465683a103d2b566f1cd"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "68fff00f775948699fd5f8e275c63dd0",
    "m_Group": {
        "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -418.0001220703125,
            "y": 2177.500244140625,
            "width": 208.00013732910157,
            "height": 338.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "7e862abfb9fb43e5914b47ee95004575"
        },
        {
            "m_Id": "ceb95632b304455fa3401e9fd187aa6c"
        },
        {
            "m_Id": "a633e8f426c14da8a12d078c562f3ef4"
        },
        {
            "m_Id": "eed7ce3373a44ea3bbdaac812f669902"
        },
        {
            "m_Id": "237d9397f36641c98467b328b11e85f7"
        },
        {
            "m_Id": "a5e09d904ce742a2b9806af841ea7580"
        },
        {
            "m_Id": "13fa792192b34935a784e6592cecfdd8"
        },
        {
            "m_Id": "b332cff06bf5469dbc5e6c22d73d9358"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "69fb2f29164f4591a0815626205990d5",
    "m_Id": 1,
    "m_DisplayName": "Width",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Width",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "69fc5a174dfc437bb07e6b092389d011",
    "m_Title": "",
    "m_Content": "An animated Fade Value that oscillates back and forth between 0 and 1.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1715.0001220703125,
        "y": 2613.000244140625,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "6cc75dbc8da74a1c9f91a9fc44030e31",
    "m_Id": 2,
    "m_DisplayName": "Color B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "ColorB",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0
    },
    "m_DefaultValue": {
        "x": 0.699999988079071,
        "y": 0.699999988079071,
        "z": 0.699999988079071
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.699999988079071,
        "g": 0.699999988079071,
        "b": 0.699999988079071,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "6d65c393cecf48ef8997368d8f8c02d6",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.20000000298023225,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6d93283ee8444f9e9e52937e4236946c",
    "m_Id": 1,
    "m_DisplayName": "Time",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "6f6c0ba00359469d968b804e01d2d476",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "6f81205adf184f5cb1f723b8d36ae88c",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "702940ab8ce1428991e272e1a2d80050",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7093818cdb1f49b7bdba4ea49a396b3f",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "71b413eeacd649578a05a40b5250276f",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "73ac945633aa4b5889019e39b4e44f08",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "740eb2cf1c9c4a7db374b115887e6c25",
    "m_Title": "",
    "m_Content": "Here we're using Time to animate the Offset value of the Twirl node. We're using twirl to adjust the UV coordinates that we pass into the Checkerboard node.\n\nWe're also using Time to animate the colors of the checkerboard - fluctuating back and forth between orange and purple, and blue and yellow.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1161.5001220703125,
        "y": 743.5000610351563,
        "width": 200.00006103515626,
        "height": 171.5
    },
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "7427feac96b34198a1029e63e2882b9d",
    "m_Title": "",
    "m_Content": "Using the Absolute node, now our data goes from 1 to 0 and back to 1, so it's more like a bounce than a wave.\n\nThe same thing could also be done with vertex position to actually make your model bounce.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1433.5001220703125,
        "y": 791.5000610351563,
        "width": 200.0,
        "height": 131.0
    },
    "m_Group": {
        "m_Id": "4e9adb178a834c5c81430c79ede15533"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "7431ee03c6a64abca6596d16f2b7cb25",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"ade6c320c9b59a94d9bb43c5a437e2ef\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "761d5a85493d4971a643e1b4b378f1a2",
    "m_Title": "",
    "m_Content": "Measuring the distance between the animated point and the object space position creates a gradient that's dark when the point is close to the pivot and bright when it's further away.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1221.5001220703125,
        "y": 1878.5001220703125,
        "width": 200.00006103515626,
        "height": 110.5
    },
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "78023b64834348b1afce8370d93d6817",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "781f7cf357c64ba6a9550cb8f9e95769",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 2.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "786e7bc4177b47129a99cf85ee6667a1",
    "m_Title": "Flipbook",
    "m_Position": {
        "x": -1046.00048828125,
        "y": 2118.999755859375
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.SampleGradient",
    "m_ObjectId": "7899c8939cfe433b84e7eb65a2410933",
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    },
    "m_Name": "Sample Gradient",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -753.5000610351563,
            "y": 646.5,
            "width": 167.99993896484376,
            "height": 118.00006103515625
        }
    },
    "m_Slots": [
        {
            "m_Id": "3b3a1ac8fdbb422b928a1db8ba50cbd3"
        },
        {
            "m_Id": "6d93283ee8444f9e9e52937e4236946c"
        },
        {
            "m_Id": "fd4fcbca360d4d108e1233c58638e763"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "7a917417a8e24122bcf471f469cfbdee",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1509.5001220703125,
            "y": 1201.5001220703125,
            "width": 208.0001220703125,
            "height": 277.9998779296875
        }
    },
    "m_Slots": [
        {
            "m_Id": "27d437bd201e4bfa93e64b6a8ef2a06d"
        },
        {
            "m_Id": "e3c317117a8b4582949fbb4b05821faa"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7c945ac586004df4a6109e82cbb688a7",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "7d7fd8df441b4c2681f8872c64512f66",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "7e862abfb9fb43e5914b47ee95004575",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "7ecd825bc5974367abb516171c6bf27c",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FractionNode",
    "m_ObjectId": "7f1f1894a1224a3e98ec61dac56c72b2",
    "m_Group": {
        "m_Id": "02b4f2070ec54739a9427c41ef11eac3"
    },
    "m_Name": "Fraction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1086.500244140625,
            "y": 22.000001907348634,
            "width": 208.0001220703125,
            "height": 278.0000305175781
        }
    },
    "m_Slots": [
        {
            "m_Id": "93dc99def21f4663a9aae1c9c2596cc1"
        },
        {
            "m_Id": "caba95584a244350956e5046bb033852"
        }
    ],
    "synonyms": [
        "remainder"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "7f5dc8d00aae4a85b9da8ed386f063a5",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PolarCoordinatesNode",
    "m_ObjectId": "803c409474ba4a878b8a051bf9badb14",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Polar Coordinates",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1786.000244140625,
            "y": 1201.5001220703125,
            "width": 147.5001220703125,
            "height": 93.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d3a4d76ecf9c4aacab067bb58c77c8b9"
        },
        {
            "m_Id": "9679164735d44b9d92255e80bce89754"
        },
        {
            "m_Id": "29298eec456e4cc89244c2468a6d471a"
        },
        {
            "m_Id": "e0f069bb33554f078c76a0e72ed545c9"
        },
        {
            "m_Id": "9f0e8e8e7a4044778378a79f14a1fd24"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "80438f0bc84b455a985d5aa795748443",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PowerNode",
    "m_ObjectId": "807ef4cf2e164175805bdfff9a48de98",
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    },
    "m_Name": "Power",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -731.0001831054688,
            "y": 1759.000244140625,
            "width": 208.0001220703125,
            "height": 302.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "2c01faf27f7a4b748d7584c34a621fa7"
        },
        {
            "m_Id": "c6ef0f2a6a65432197463bfb5d925dc8"
        },
        {
            "m_Id": "ae10e9240f1144c8848e351b06569eff"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "809be3a0334a402c9a4d15fef294d094",
    "m_Id": 2,
    "m_DisplayName": "Height",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Height",
    "m_StageCapability": 3,
    "m_Value": 4.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "811b8d742c164390a36cfd00f2a88174",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "820a606f1deb46b29d3dcd34b84c8f9a",
    "m_Group": {
        "m_Id": "bf7e0d211350434993a2fe4fd8a69fec"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1455.0,
            "y": 21.00001335144043,
            "width": 207.999755859375,
            "height": 337.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "7d7fd8df441b4c2681f8872c64512f66"
        },
        {
            "m_Id": "4636548462e84e588de4cceb2c65b4f3"
        },
        {
            "m_Id": "d0b77b886c1742e4af1e861c2f257ee3"
        },
        {
            "m_Id": "cc5162140cf744fd9a819e684ba5a2ee"
        },
        {
            "m_Id": "ed1dbef1d2c74db896f1ca34c9bbd922"
        },
        {
            "m_Id": "7431ee03c6a64abca6596d16f2b7cb25"
        },
        {
            "m_Id": "921bf0a4d82e4ea6ba7b2717a2878780"
        },
        {
            "m_Id": "a23c4c7c0be342fea7a4b69818603888"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "8284cf3176c14ddeb144718306ffe194",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -535.500244140625,
            "y": 1176.0001220703125,
            "width": 126.00030517578125,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d16bfa52f1d84b809dc5ca9fff7977e7"
        },
        {
            "m_Id": "781f7cf357c64ba6a9550cb8f9e95769"
        },
        {
            "m_Id": "318ee9f4873646139583a26302d430eb"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "82b0b2cce35a44a99387a852a75a8dcb",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "83ae06f5792b4f9f8b6265b446e82cff",
    "m_Title": "Animated Rotation",
    "m_Position": {
        "x": -481.50042724609377,
        "y": 1597.000244140625
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "84f997454d854e0bbc45ad643815ae48",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "87bba153b8364b6d88987d249f2b464c",
    "m_Id": 3,
    "m_DisplayName": "Tile",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tile",
    "m_StageCapability": 3,
    "m_Value": 6.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "883dacc0d5eb43fc81ede3d68d3ecf3c",
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1165.5,
            "y": 502.5000915527344,
            "width": 78.999755859375,
            "height": 75.99990844726563
        }
    },
    "m_Slots": [
        {
            "m_Id": "05bf5a1b4ded4c548049d71e16c9dcbf"
        },
        {
            "m_Id": "3c55cc25a62545e096fb9694d72c41bc"
        },
        {
            "m_Id": "403bf57b66064287a89dca669eb940c6"
        },
        {
            "m_Id": "7f5dc8d00aae4a85b9da8ed386f063a5"
        },
        {
            "m_Id": "007286d5e6e441d29167de4c0d2edce8"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "8a768a35cca345d3a6c3e864535ffb87",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "8b626763087047af82e9440d7c01b3b7",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "8d94e6b7004345479c35a91e2d0d9513",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "905654f4eb934506a4123932b6108d6a",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "9190b0e90ffc49149083c8563c744667",
    "m_Id": 3,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "921bf0a4d82e4ea6ba7b2717a2878780",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "9259c89a5b564d368d8dc13b2808dd32",
    "m_Group": {
        "m_Id": "bf7e0d211350434993a2fe4fd8a69fec"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1706.5001220703125,
            "y": 21.00001335144043,
            "width": 78.9998779296875,
            "height": 75.99998474121094
        }
    },
    "m_Slots": [
        {
            "m_Id": "216b29732a1e4c958ebd568628b8898f"
        },
        {
            "m_Id": "2e4bfb8c17d444e19d130ea23f00f902"
        },
        {
            "m_Id": "f2dddffb24b34aa0a48a37532f7796df"
        },
        {
            "m_Id": "4eaf9dc379ee45b692c56cd839f22ff6"
        },
        {
            "m_Id": "a6e845869dd54bb0964a95a4bcc9e713"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "93dc99def21f4663a9aae1c9c2596cc1",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "95b681eecd4045ea964e0e51d3e56da2",
    "m_Id": 1,
    "m_DisplayName": "Center",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Center",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.5,
        "y": 0.5
    },
    "m_DefaultValue": {
        "x": 0.5,
        "y": 0.5
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubtractNode",
    "m_ObjectId": "9630af6c34f14c21a259fb93f0e678e9",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Subtract",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1427.5003662109375,
            "y": 1083.5001220703125,
            "width": 126.0003662109375,
            "height": 118.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9a2f6b332074451d9ca616f1c974255b"
        },
        {
            "m_Id": "78023b64834348b1afce8370d93d6817"
        },
        {
            "m_Id": "5023c4c2282343f488e6efdde0cf7913"
        }
    ],
    "synonyms": [
        "subtraction",
        "remove",
        "minus",
        "take away"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "9679164735d44b9d92255e80bce89754",
    "m_Id": 1,
    "m_DisplayName": "Center",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Center",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.5,
        "y": 0.5
    },
    "m_DefaultValue": {
        "x": 0.5,
        "y": 0.5
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PositionMaterialSlot",
    "m_ObjectId": "9a1ac83b50064ed3b72f106a2edc920e",
    "m_Id": 0,
    "m_DisplayName": "Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Position",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "9a2f6b332074451d9ca616f1c974255b",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9ab66c5d7f90490a87ea72444efcb264",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "9bddbeff5b7d45adbe8d59563b1d13fd",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "9c9d538994cb48158f706724c87141e0",
    "m_Title": "",
    "m_Content": "Using Sine Time can create a smooth, animated wave where the time value oscillates between -1 and 1.  Here we've adjusted that range to 0 to 1 so we can visualize it.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -730.0000610351563,
        "y": 188.50001525878907,
        "width": 200.0,
        "height": 100.00001525878906
    },
    "m_Group": {
        "m_Id": "bc2702169ebf48c88fc96efb659ec121"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.NormalMaterialSlot",
    "m_ObjectId": "9d2575bb808d45bb9a48357a59ca2944",
    "m_Id": 0,
    "m_DisplayName": "Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Normal",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVNode",
    "m_ObjectId": "9ed756652bad4c809a9159b71d05e5d7",
    "m_Group": {
        "m_Id": "bf7e0d211350434993a2fe4fd8a69fec"
    },
    "m_Name": "UV",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1772.5001220703125,
            "y": 98.00006866455078,
            "width": 144.9998779296875,
            "height": 128.49996948242188
        }
    },
    "m_Slots": [
        {
            "m_Id": "60291e712d944009a7b7b6096a406eee"
        }
    ],
    "synonyms": [
        "texcoords",
        "coords",
        "coordinates"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_OutputChannel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "9f0e8e8e7a4044778378a79f14a1fd24",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AbsoluteNode",
    "m_ObjectId": "9f5d9e187c0d4530ab6fc4ea9a89382e",
    "m_Group": {
        "m_Id": "4e9adb178a834c5c81430c79ede15533"
    },
    "m_Name": "Absolute",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1433.0001220703125,
            "y": 502.5000915527344,
            "width": 208.0001220703125,
            "height": 277.9999084472656
        }
    },
    "m_Slots": [
        {
            "m_Id": "9bddbeff5b7d45adbe8d59563b1d13fd"
        },
        {
            "m_Id": "154a057b390849c993afc7c15ea4c8b6"
        }
    ],
    "synonyms": [
        "positive"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AddNode",
    "m_ObjectId": "a0cc344958f14f1c8cd0f583f231763b",
    "m_Group": {
        "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
    },
    "m_Name": "Add",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1556.000244140625,
            "y": 2515.000244140625,
            "width": 126.0001220703125,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e8e7003b5e6e428b88687d400fb0dccf"
        },
        {
            "m_Id": "fc37bed525a248fabaf2eec9da288915"
        },
        {
            "m_Id": "650e5e1f9f2740a8adaf8c157c2aff14"
        }
    ],
    "synonyms": [
        "addition",
        "sum",
        "plus"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a0fc3ec46ac347fa8159015947ca0314",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a13b6558e0e84b2e9eba71d1dc2cfb81",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "a23c4c7c0be342fea7a4b69818603888",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "a321af2b04a4406f9bc05c8313a3fe98",
    "m_Title": "Ripples",
    "m_Position": {
        "x": -1811.000244140625,
        "y": 990.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a3546754ccb948d9947630da4bb08356",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.CategoryData",
    "m_ObjectId": "a4bc3c0d75ac468a881e50bf9a33dae7",
    "m_Name": "",
    "m_ChildObjectList": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "a4ca3fb7971441eb8916dc91f8449a76",
    "m_Id": 1,
    "m_DisplayName": "Center",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Center",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.5,
        "y": 0.5
    },
    "m_DefaultValue": {
        "x": 0.5,
        "y": 0.5
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "a5b563431c914cc4b3128ce755be69e2",
    "m_Title": "",
    "m_Content": "In this example, the Sine of time gives us a value that oscilates back and forth between 1 and -1.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1668.0001220703125,
        "y": 785.5000610351563,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "4e9adb178a834c5c81430c79ede15533"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "a5e09d904ce742a2b9806af841ea7580",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"b8aa35b1f4ce5ee5bbf0376cb9eb10cb\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a633e8f426c14da8a12d078c562f3ef4",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "a642851d4a914b1e99e197091b271113",
    "m_Group": {
        "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1786.5001220703125,
            "y": 2515.000244140625,
            "width": 104.5,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "284cab90429c443ba94d4e341979e960"
        },
        {
            "m_Id": "047ecf1a501e4602a885871d02449585"
        },
        {
            "m_Id": "cf4213b4493c461d952c4cb2bcaa23c6"
        },
        {
            "m_Id": "fc96ef82b499417087d85d03585bb6a0"
        },
        {
            "m_Id": "ebab1b09c6da4866b0a326032b421b46"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a691c14bdedd4a5d8146f0f840e88cbe",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a6e845869dd54bb0964a95a4bcc9e713",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a6fec41608fe48f0bed52ebca23bd41d",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a734f72edee149329517a97b9315e00a",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "a79722ecd2d44a45a4011951051bd4e5",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a94a057607314147b1c60135fc79f9ff",
    "m_Id": 0,
    "m_DisplayName": "Fade",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Fade",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "a94a11f43f574a36a98505f06ad97176",
    "m_Id": 0,
    "m_DisplayName": "Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ae10e9240f1144c8848e351b06569eff",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "ae579837e5204b329ea0652adfc1e10f",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "af74d331682344a88e3a5dd0c1b5729e",
    "m_Id": 1,
    "m_DisplayName": "Color A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "ColorA",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.20000000298023225,
        "y": 0.20000000298023225,
        "z": 0.20000000298023225
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.20000000298023225,
        "g": 0.20000000298023225,
        "b": 0.20000000298023225,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "af8d41599c6e42fda5c05a0a7f74bae6",
    "m_Title": "",
    "m_Content": "Adding Time to UV coordinates can be used to scroll textures.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1721.0001220703125,
        "y": 247.00003051757813,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "bf7e0d211350434993a2fe4fd8a69fec"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "afb24548dad74d8e8ee270fe888555fb",
    "m_Title": "Time Node",
    "m_Content": "The Time node is the basis for all animated effects.  The Time output is a value that continually increases over time - so the longer the project is running, the higher the value gets.\n\nThe Sine Time output is a continously updating wave that fluctuates between -1 and 1.\n\nCosine Time is also a continously updating wave - similar to Sine Time, but it's offset by a quater of a phase.\n\nDelta Time is the time difference between the current frame and the last frame.\n\nSmooth Delta is similar to Delta Time, but it has been smoothed so it's not as jagged when the frame rate changes quickly.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1107.5001220703125,
        "y": -295.0000305175781,
        "width": 477.50006103515627,
        "height": 208.00001525878907
    },
    "m_Group": {
        "m_Id": ""
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "b21b88f416364377b4b17ecf6148bb07",
    "m_Title": "Something More Interesting",
    "m_Position": {
        "x": -1190.5001220703125,
        "y": 444.00018310546877
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SamplerStateMaterialSlot",
    "m_ObjectId": "b332cff06bf5469dbc5e6c22d73d9358",
    "m_Id": 3,
    "m_DisplayName": "Sampler",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sampler",
    "m_StageCapability": 3,
    "m_BareResource": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b423922489fb4d4786462a6a4c46f85b",
    "m_Id": 3,
    "m_DisplayName": "Z",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Z",
    "m_StageCapability": 3,
    "m_Value": -0.5,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "Z"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b6c3520dac914c15b70a25e9fd32af0f",
    "m_Id": 1,
    "m_DisplayName": "NoiseValue",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "NoiseValue",
    "m_StageCapability": 2,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b78b2af1c45b4d9ba502a74e88257b32",
    "m_Id": 1,
    "m_DisplayName": "X",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "X",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "b7ea3b7486c14f5d8f3db3f81e80515e",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "b820ea4f4fd243428a4320ed66780ec0",
    "m_Title": "",
    "m_Content": "Saturating and inverting the gradient gives us a mask that looks like a light source inside the volume.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -938.5000610351563,
        "y": 1889.5001220703125,
        "width": 200.0,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "b858c4a52f974993b78140c95b6cc9ca",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b8e09b0a54114be19340c0ce87747c46",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b8e3d496cba04e7f91d02e460bf0c10c",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "b9cbae335ad3478cb9d0cfeca317a7df",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 20.0,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "bc2702169ebf48c88fc96efb659ec121",
    "m_Title": "Time Waves",
    "m_Position": {
        "x": -846.5005493164063,
        "y": -36.999855041503909
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "be39858cb10a48a6a83056141b92a460",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1666.5001220703125,
            "y": 1048.5001220703125,
            "width": 78.9998779296875,
            "height": 76.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "a94a11f43f574a36a98505f06ad97176"
        },
        {
            "m_Id": "ce966ad3b98545039736cefd57d3c530"
        },
        {
            "m_Id": "c8b7aeca120a402d9523744732491905"
        },
        {
            "m_Id": "2cf953f7046545e8beffe199df7d4a8e"
        },
        {
            "m_Id": "c26f739710384a65bf5d0ba718494981"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "bf7e0d211350434993a2fe4fd8a69fec",
    "m_Title": "Scrolling",
    "m_Position": {
        "x": -1797.5,
        "y": -37.499916076660159
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "c039fd517fdd4eaca7869e4d9e7f5e62",
    "m_Group": {
        "m_Id": "b21b88f416364377b4b17ecf6148bb07"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1086.500244140625,
            "y": 502.5000915527344,
            "width": 126.00030517578125,
            "height": 94.00003051757813
        }
    },
    "m_Slots": [
        {
            "m_Id": "6f6c0ba00359469d968b804e01d2d476"
        },
        {
            "m_Id": "6d65c393cecf48ef8997368d8f8c02d6"
        },
        {
            "m_Id": "b858c4a52f974993b78140c95b6cc9ca"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GradientInputMaterialSlot",
    "m_ObjectId": "c1cac5fae26f4d1da8e87ebb08c2d1d3",
    "m_Id": 0,
    "m_DisplayName": "Gradient",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Gradient",
    "m_StageCapability": 3,
    "m_Value": {
        "serializedVersion": "2",
        "key0": {
            "r": 0.0,
            "g": 0.0027883052825927736,
            "b": 1.0,
            "a": 1.0
        },
        "key1": {
            "r": 0.993972897529602,
            "g": 1.0,
            "b": 0.0,
            "a": 1.0
        },
        "key2": {
            "r": 0.0,
            "g": 0.003460407257080078,
            "b": 1.0,
            "a": 0.0
        },
        "key3": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key4": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key5": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key6": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key7": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "ctime0": 0,
        "ctime1": 32767,
        "ctime2": 65535,
        "ctime3": 0,
        "ctime4": 0,
        "ctime5": 0,
        "ctime6": 0,
        "ctime7": 0,
        "atime0": 0,
        "atime1": 65535,
        "atime2": 0,
        "atime3": 0,
        "atime4": 0,
        "atime5": 0,
        "atime6": 0,
        "atime7": 0,
        "m_Mode": 0,
        "m_ColorSpace": -1,
        "m_NumColorKeys": 3,
        "m_NumAlphaKeys": 2
    },
    "m_DefaultValue": {
        "serializedVersion": "2",
        "key0": {
            "r": 1.0,
            "g": 1.0,
            "b": 1.0,
            "a": 1.0
        },
        "key1": {
            "r": 1.0,
            "g": 1.0,
            "b": 1.0,
            "a": 1.0
        },
        "key2": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key3": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key4": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key5": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key6": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "key7": {
            "r": 0.0,
            "g": 0.0,
            "b": 0.0,
            "a": 0.0
        },
        "ctime0": 0,
        "ctime1": 65535,
        "ctime2": 0,
        "ctime3": 0,
        "ctime4": 0,
        "ctime5": 0,
        "ctime6": 0,
        "ctime7": 0,
        "atime0": 0,
        "atime1": 65535,
        "atime2": 0,
        "atime3": 0,
        "atime4": 0,
        "atime5": 0,
        "atime6": 0,
        "atime7": 0,
        "m_Mode": 0,
        "m_ColorSpace": -1,
        "m_NumColorKeys": 2,
        "m_NumAlphaKeys": 2
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c1deba1dc1054cd191d0a029ca4d6c1f",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TangentMaterialSlot",
    "m_ObjectId": "c259fb0568ea4db39df97f01128b0e08",
    "m_Id": 0,
    "m_DisplayName": "Tangent",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Tangent",
    "m_StageCapability": 1,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_Space": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c26f739710384a65bf5d0ba718494981",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "c27750ae5fd048a5b42e782035d8502d",
    "m_Id": 4,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "c325f3cd9a7c434a96b26960b315761b",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FlipbookNode",
    "m_ObjectId": "c52bfbf731c04b9d8fb2d2ecc4e92309",
    "m_Group": {
        "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
    },
    "m_Name": "Flipbook",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -593.5,
            "y": 2177.500244140625,
            "width": 160.00003051757813,
            "height": 227.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "063db92972f643cabfcb708a143f78fd"
        },
        {
            "m_Id": "69fb2f29164f4591a0815626205990d5"
        },
        {
            "m_Id": "809be3a0334a402c9a4d15fef294d094"
        },
        {
            "m_Id": "87bba153b8364b6d88987d249f2b464c"
        },
        {
            "m_Id": "c27750ae5fd048a5b42e782035d8502d"
        }
    ],
    "synonyms": [
        "atlas",
        "animation"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_InvertX": false,
    "m_InvertY": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "c6ef0f2a6a65432197463bfb5d925dc8",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 8.0,
        "y": 2.0,
        "z": 2.0,
        "w": 2.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c71bea4ad39e4fe0892a118dddd81a0a",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "c74d9a7a86a040d480ee4d56fc63e5c8",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c75b799937d9464e8bfaea96d4c2fe48",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "c8b7aeca120a402d9523744732491905",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Texture2DInputMaterialSlot",
    "m_ObjectId": "ca836c83a26c469cb742ede8d4eacacc",
    "m_Id": 1,
    "m_DisplayName": "Texture",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Texture",
    "m_StageCapability": 3,
    "m_BareResource": false,
    "m_Texture": {
        "m_SerializedTexture": "{\"texture\":{\"fileID\":2800000,\"guid\":\"b8aa35b1f4ce5ee5bbf0376cb9eb10cb\",\"type\":3}}",
        "m_Guid": ""
    },
    "m_DefaultType": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "caba95584a244350956e5046bb033852",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.OneMinusNode",
    "m_ObjectId": "cbac6445fa2c45ac9bdc2bd9712169d4",
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    },
    "m_Name": "One Minus",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -889.0001220703125,
            "y": 1717.5001220703125,
            "width": 127.50018310546875,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "f2e15c5787934c59b34e9addff8e541e"
        },
        {
            "m_Id": "82b0b2cce35a44a99387a852a75a8dcb"
        }
    ],
    "synonyms": [
        "complement",
        "invert",
        "opposite"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cc5162140cf744fd9a819e684ba5a2ee",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.GroupData",
    "m_ObjectId": "ccd0a374c79546e68be204d3c76c7eb0",
    "m_Title": "Artificial Internal Light Source",
    "m_Position": {
        "x": -1808.5001220703125,
        "y": 1597.500244140625
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ce966ad3b98545039736cefd57d3c530",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector2MaterialSlot",
    "m_ObjectId": "ceb08644c41f4ec884e94474a4e07b79",
    "m_Id": 3,
    "m_DisplayName": "Offset",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Offset",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ceb95632b304455fa3401e9fd187aa6c",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "cf4213b4493c461d952c4cb2bcaa23c6",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d0b77b886c1742e4af1e861c2f257ee3",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "d16bfa52f1d84b809dc5ca9fff7977e7",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 0.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 0.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 0.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.ColorRGBMaterialSlot",
    "m_ObjectId": "d2b5d7c271d744a7bf4f56bb81139927",
    "m_Id": 0,
    "m_DisplayName": "Base Color",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "BaseColor",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.5,
        "y": 0.5,
        "z": 0.5
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [],
    "m_ColorMode": 0,
    "m_DefaultColor": {
        "r": 0.5,
        "g": 0.5,
        "b": 0.5,
        "a": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "d3634b4f576d4601bf7fb3816be36a14",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.5,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "d3a4d76ecf9c4aacab067bb58c77c8b9",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d5d5c60e46574d509725bc43729491f7",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d5ded0338f544274b411e090f5f3e814",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "d740e42f79bf43cc8e3c97b97b020a74",
    "m_Id": 3,
    "m_DisplayName": "FadeContrast",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "FadeContrast",
    "m_StageCapability": 2,
    "m_Value": 300.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "d8eeef681c7f488ab5eacc23f07ac4aa",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicValueMaterialSlot",
    "m_ObjectId": "da26233690484d4690c9ad052004fc23",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "e00": 0.5,
        "e01": 2.0,
        "e02": 2.0,
        "e03": 2.0,
        "e10": 2.0,
        "e11": 2.0,
        "e12": 2.0,
        "e13": 2.0,
        "e20": 2.0,
        "e21": 2.0,
        "e22": 2.0,
        "e23": 2.0,
        "e30": 2.0,
        "e31": 2.0,
        "e32": 2.0,
        "e33": 2.0
    },
    "m_DefaultValue": {
        "e00": 1.0,
        "e01": 0.0,
        "e02": 0.0,
        "e03": 0.0,
        "e10": 0.0,
        "e11": 1.0,
        "e12": 0.0,
        "e13": 0.0,
        "e20": 0.0,
        "e21": 0.0,
        "e22": 1.0,
        "e23": 0.0,
        "e30": 0.0,
        "e31": 0.0,
        "e32": 0.0,
        "e33": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "da85bac33cf94aa4abb6b4b1c8ba90e9",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "da9d05b7f0e44b42bc0545bca0c1f476",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "db214d0e6ff147f7999d472bcbaea8db",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "ddbf96690e924eadacdac30155a17089",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1245.5,
            "y": -299.0,
            "width": 123.999755859375,
            "height": 173.00006103515626
        }
    },
    "m_Slots": [
        {
            "m_Id": "7093818cdb1f49b7bdba4ea49a396b3f"
        },
        {
            "m_Id": "b8e09b0a54114be19340c0ce87747c46"
        },
        {
            "m_Id": "5989759020bf4e189ccdd8204992bff5"
        },
        {
            "m_Id": "fa99e069f75b46999abc1c1adf789e26"
        },
        {
            "m_Id": "e46458a71b1d4a7c927ae4e5dc94f534"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e015519de9144c2d9f473d4a4723c3ad",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e021bdd7648445a0a9f8f999634dd7db",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "e0e771cc866b4d36809d98aeed0e3931",
    "m_Group": {
        "m_Id": "83ae06f5792b4f9f8b6265b446e82cff"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -169.50018310546876,
            "y": 1655.5001220703125,
            "width": 208.0001220703125,
            "height": 338.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "ef6518e8c4224a19b80b02d06b9f6fa8"
        },
        {
            "m_Id": "20fe509050654cd689a7d51981b35c54"
        },
        {
            "m_Id": "3322d89a835d4c359c975c852725c518"
        },
        {
            "m_Id": "80438f0bc84b455a985d5aa795748443"
        },
        {
            "m_Id": "fe78923f6c354f999e6d3bd988c98f85"
        },
        {
            "m_Id": "ca836c83a26c469cb742ede8d4eacacc"
        },
        {
            "m_Id": "404fb29806cb4de8a2570dc1cf062796"
        },
        {
            "m_Id": "811b8d742c164390a36cfd00f2a88174"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SaturateNode",
    "m_ObjectId": "e0eea81098974f8a9415004c0ac3f519",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Saturate",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1050.5,
            "y": 1128.0001220703125,
            "width": 127.49981689453125,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "eeff5cb6b7d54471acc629ae6e6ad09d"
        },
        {
            "m_Id": "e8522e03cd2a4d7eb9be1e87e64fc51f"
        }
    ],
    "synonyms": [
        "clamp"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e0f069bb33554f078c76a0e72ed545c9",
    "m_Id": 3,
    "m_DisplayName": "Length Scale",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "LengthScale",
    "m_StageCapability": 3,
    "m_Value": 1.0,
    "m_DefaultValue": 1.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e0fdb9ec65a247c798b95e9d596b8c3a",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e3aab873c64c4e81903723b91ef662d4",
    "m_Id": 2,
    "m_DisplayName": "Rotation",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "Rotation",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e3c317117a8b4582949fbb4b05821faa",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e46458a71b1d4a7c927ae4e5dc94f534",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e660441d79ad463781b3c84b89ed01f1",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e6b8703732dd4d78bfbad98046414c59",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e8522e03cd2a4d7eb9be1e87e64fc51f",
    "m_Id": 1,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "e8e7003b5e6e428b88687d400fb0dccf",
    "m_Id": 0,
    "m_DisplayName": "A",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e9097f29f29a48c1914f4219087ad18e",
    "m_Id": 1,
    "m_DisplayName": "Sine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Sine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "e99f0907e6c941c89c9c7bf5c8cc0883",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.Alpha",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "39b2f23265134506bc876a96137c9add"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.Alpha"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SineNode",
    "m_ObjectId": "e9a04e7a29b0410082f2cb3796064c71",
    "m_Group": {
        "m_Id": "4e9adb178a834c5c81430c79ede15533"
    },
    "m_Name": "Sine",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1668.5001220703125,
            "y": 502.5000915527344,
            "width": 208.0,
            "height": 277.9999084472656
        }
    },
    "m_Slots": [
        {
            "m_Id": "5e3b2ffb41e04b6f804862c79b07ce28"
        },
        {
            "m_Id": "6317bc3d66734be7a1878c86b42d9b0c"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e9b9ad8ffe3e405589d8e870e29e4c92",
    "m_Id": 4,
    "m_DisplayName": "R",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "R",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "ea199312bbfc4b6ba8af869e60e80198",
    "m_Title": "",
    "m_Content": "First we enter the number of rows and columns in the Width and Height inputs.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -804.0000610351563,
        "y": 2190.500244140625,
        "width": 113.5,
        "height": 100.0
    },
    "m_Group": {
        "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ebab1b09c6da4866b0a326032b421b46",
    "m_Id": 4,
    "m_DisplayName": "Smooth Delta",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Smooth Delta",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "ed1dbef1d2c74db896f1ca34c9bbd922",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "eed7ce3373a44ea3bbdaac812f669902",
    "m_Id": 6,
    "m_DisplayName": "B",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "eeff5cb6b7d54471acc629ae6e6ad09d",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "ef4f7e32776c433991c077bce7dbfce3",
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1765.5001220703125,
            "y": 1787.5001220703125,
            "width": 104.5,
            "height": 76.0001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "328456c6cfbd40498ae78b8617fdd368"
        },
        {
            "m_Id": "3418ebd5741a4104b5e30b5910d07f2b"
        },
        {
            "m_Id": "84f997454d854e0bbc45ad643815ae48"
        },
        {
            "m_Id": "a3546754ccb948d9947630da4bb08356"
        },
        {
            "m_Id": "8b626763087047af82e9440d7c01b3b7"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "ef6518e8c4224a19b80b02d06b9f6fa8",
    "m_Id": 0,
    "m_DisplayName": "RGBA",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "RGBA",
    "m_StageCapability": 2,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.TimeNode",
    "m_ObjectId": "efd1c09399d64609ab01094c21b10db3",
    "m_Group": {
        "m_Id": "83ae06f5792b4f9f8b6265b446e82cff"
    },
    "m_Name": "Time",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -456.5000915527344,
            "y": 1655.5001220703125,
            "width": 79.0001220703125,
            "height": 76.9998779296875
        }
    },
    "m_Slots": [
        {
            "m_Id": "905654f4eb934506a4123932b6108d6a"
        },
        {
            "m_Id": "9ab66c5d7f90490a87ea72444efcb264"
        },
        {
            "m_Id": "0fc2bbb1ce5c44ba964379f36fba8a0d"
        },
        {
            "m_Id": "db214d0e6ff147f7999d472bcbaea8db"
        },
        {
            "m_Id": "a13b6558e0e84b2e9eba71d1dc2cfb81"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3Node",
    "m_ObjectId": "eff67640ac57406b82f343f1574be2f7",
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    },
    "m_Name": "Vector 3",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1466.500244140625,
            "y": 1787.5001220703125,
            "width": 127.5001220703125,
            "height": 125.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "b78b2af1c45b4d9ba502a74e88257b32"
        },
        {
            "m_Id": "163fbab7e8704032b677f26c9c59f3a5"
        },
        {
            "m_Id": "b423922489fb4d4786462a6a4c46f85b"
        },
        {
            "m_Id": "a79722ecd2d44a45a4011951051bd4e5"
        }
    ],
    "synonyms": [
        "3",
        "v3",
        "vec3",
        "float3"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.Rendering.Universal.ShaderGraph.UniversalTarget",
    "m_ObjectId": "f0d8c595ab14488d9b0414fee46c663c",
    "m_Datas": [],
    "m_ActiveSubTarget": {
        "m_Id": "60b1d50a561a46fe8740b78eb29842cd"
    },
    "m_AllowMaterialOverride": false,
    "m_SurfaceType": 0,
    "m_ZTestMode": 4,
    "m_ZWriteControl": 0,
    "m_AlphaMode": 0,
    "m_RenderFace": 2,
    "m_AlphaClip": false,
    "m_CastShadows": true,
    "m_ReceiveShadows": true,
    "m_DisableTint": false,
    "m_AdditionalMotionVectorMode": 0,
    "m_AlembicMotionVectors": false,
    "m_SupportsLODCrossFade": false,
    "m_CustomEditorGUI": "",
    "m_SupportVFX": false
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.AddNode",
    "m_ObjectId": "f1df6665f21e439c98f7df05aab4b773",
    "m_Group": {
        "m_Id": "bc2702169ebf48c88fc96efb659ec121"
    },
    "m_Name": "Add",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -440.0,
            "y": 21.500049591064454,
            "width": 207.99978637695313,
            "height": 301.9999694824219
        }
    },
    "m_Slots": [
        {
            "m_Id": "c1deba1dc1054cd191d0a029ca4d6c1f"
        },
        {
            "m_Id": "f88ed44df0e14b12a687c4985438d892"
        },
        {
            "m_Id": "4309b9f7515e4a679d7a487671eaec4a"
        }
    ],
    "synonyms": [
        "addition",
        "sum",
        "plus"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f28ec1f18b41482e8f478cadcb37fc31",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f2dddffb24b34aa0a48a37532f7796df",
    "m_Id": 2,
    "m_DisplayName": "Cosine Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Cosine Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f2e15c5787934c59b34e9addff8e541e",
    "m_Id": 0,
    "m_DisplayName": "In",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "In",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 1.0,
        "y": 1.0,
        "z": 1.0,
        "w": 1.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "f5ac03271a0f4ea3bd413bd955b85acd",
    "m_Id": 0,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.FractionNode",
    "m_ObjectId": "f62ea240ab5e4aab874f33767d2c8dc5",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Fraction",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1587.500244140625,
            "y": 1048.5001220703125,
            "width": 127.5,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "381f11c735524960a0bd7759ec022177"
        },
        {
            "m_Id": "e6b8703732dd4d78bfbad98046414c59"
        }
    ],
    "synonyms": [
        "remainder"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f63090608aa841298a1dd727ccd45155",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.StickyNoteData",
    "m_ObjectId": "f6b545ccbd2147ba96082dd18f66bdba",
    "m_Title": "",
    "m_Content": "Moving point inside the object. The animated value becomes the point's X coordinate - so the point is moving back and forth.",
    "m_TextSize": 0,
    "m_Theme": 0,
    "m_Position": {
        "serializedVersion": "2",
        "x": -1475.5001220703125,
        "y": 1958.5001220703125,
        "width": 200.0,
        "height": 100.0001220703125
    },
    "m_Group": {
        "m_Id": "ccd0a374c79546e68be204d3c76c7eb0"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.BlockNode",
    "m_ObjectId": "f6dca3fbec324ce6a3550e58f4b5d168",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "SurfaceDescription.BaseColor",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 0.0,
            "y": 0.0,
            "width": 0.0,
            "height": 0.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "d2b5d7c271d744a7bf4f56bb81139927"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SerializedDescriptor": "SurfaceDescription.BaseColor"
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f87f6ead74f749aa96ecb583c4f95a3d",
    "m_Id": 5,
    "m_DisplayName": "G",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "G",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "f88ed44df0e14b12a687c4985438d892",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.5,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.UVMaterialSlot",
    "m_ObjectId": "f978ac0702854acc8a3ef83cd8d3ccfd",
    "m_Id": 2,
    "m_DisplayName": "UV",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "UV",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0
    },
    "m_Labels": [],
    "m_Channel": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "f990e1cd211f4eeda0a0b67dee086272",
    "m_Id": 2,
    "m_DisplayName": "FadeValue",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "FadeValue",
    "m_StageCapability": 2,
    "m_Value": 0.5,
    "m_DefaultValue": 0.5,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SampleTexture2DNode",
    "m_ObjectId": "f9c71c5189ce4912a2293fbba9a7b31c",
    "m_Group": {
        "m_Id": "786e7bc4177b47129a99cf85ee6667a1"
    },
    "m_Name": "Sample Texture 2D",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1021.0001831054688,
            "y": 2177.500244140625,
            "width": 208.0001220703125,
            "height": 314.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "600ddd7ef952465aaeaf767544216012"
        },
        {
            "m_Id": "6f81205adf184f5cb1f723b8d36ae88c"
        },
        {
            "m_Id": "05683763997141609f359cc4d294fb00"
        },
        {
            "m_Id": "702940ab8ce1428991e272e1a2d80050"
        },
        {
            "m_Id": "6260951ba20b44a491a76e771b721d84"
        },
        {
            "m_Id": "1f05c446661a4251b0a6ea183d22532b"
        },
        {
            "m_Id": "c74d9a7a86a040d480ee4d56fc63e5c8"
        },
        {
            "m_Id": "0a804920bb754e9eb266ae972b947a47"
        }
    ],
    "synonyms": [
        "tex2d"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_TextureType": 0,
    "m_NormalMapSpace": 0,
    "m_EnableGlobalMipBias": true,
    "m_MipSamplingMode": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fa99e069f75b46999abc1c1adf789e26",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.RotateNode",
    "m_ObjectId": "fad0dc48a0974df2bf9587aaa8c841d2",
    "m_Group": {
        "m_Id": "83ae06f5792b4f9f8b6265b446e82cff"
    },
    "m_Name": "Rotate",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -377.4999694824219,
            "y": 1655.5001220703125,
            "width": 207.99978637695313,
            "height": 312.5001220703125
        }
    },
    "m_Slots": [
        {
            "m_Id": "f5ac03271a0f4ea3bd413bd955b85acd"
        },
        {
            "m_Id": "a4ca3fb7971441eb8916dc91f8449a76"
        },
        {
            "m_Id": "e3aab873c64c4e81903723b91ef662d4"
        },
        {
            "m_Id": "9190b0e90ffc49149083c8563c744667"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Unit": 0
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.DynamicVectorMaterialSlot",
    "m_ObjectId": "fc37bed525a248fabaf2eec9da288915",
    "m_Id": 1,
    "m_DisplayName": "B",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "B",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.5,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fc96ef82b499417087d85d03585bb6a0",
    "m_Id": 3,
    "m_DisplayName": "Delta Time",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Delta Time",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.MultiplyNode",
    "m_ObjectId": "fd313955e8ab474ca1beda45c1a6a27d",
    "m_Group": {
        "m_Id": "06faa2cf629e4f919ce32bbba71670ad"
    },
    "m_Name": "Multiply",
    "m_DrawState": {
        "m_Expanded": false,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1682.0001220703125,
            "y": 2515.000244140625,
            "width": 125.9998779296875,
            "height": 94.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "8d94e6b7004345479c35a91e2d0d9513"
        },
        {
            "m_Id": "da26233690484d4690c9ad052004fc23"
        },
        {
            "m_Id": "b7ea3b7486c14f5d8f3db3f81e80515e"
        }
    ],
    "synonyms": [
        "multiplication",
        "times",
        "x"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "fd4fcbca360d4d108e1233c58638e763",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "fe78923f6c354f999e6d3bd988c98f85",
    "m_Id": 7,
    "m_DisplayName": "A",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "A",
    "m_StageCapability": 2,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector4MaterialSlot",
    "m_ObjectId": "ff2f98b894ba465683a103d2b566f1cd",
    "m_Id": 2,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    },
    "m_Labels": []
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.SwizzleNode",
    "m_ObjectId": "ffb47db5e0fe40ae92a8edf51d2c35a9",
    "m_Group": {
        "m_Id": "a321af2b04a4406f9bc05c8313a3fe98"
    },
    "m_Name": "Swizzle",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -1638.5001220703125,
            "y": 1201.5001220703125,
            "width": 129.0,
            "height": 121.5
        }
    },
    "m_Slots": [
        {
            "m_Id": "51e23f16bb424ebf8f52d2d6403caafb"
        },
        {
            "m_Id": "3d450ff1b3ca4245902d11f83f665298"
        }
    ],
    "synonyms": [
        "swap",
        "reorder",
        "component mask"
    ],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_DismissedVersion": 0,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "_maskInput": "x",
    "convertedMask": "x"
}

